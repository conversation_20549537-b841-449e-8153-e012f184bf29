{"openapi": "3.0.1", "info": {"title": "PE API", "version": "v1"}, "paths": {"/api/abp/api-definition": {"get": {"tags": ["AbpApiDefinition"], "parameters": [{"name": "IncludeTypes", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/api/abp/application-configuration": {"get": {"tags": ["AbpApplicationConfiguration"], "parameters": [{"name": "IncludeLocalizationResources", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/api/abp/application-localization": {"get": {"tags": ["AbpApplicationLocalization"], "parameters": [{"name": "CultureName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "OnlyDynamics", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/api/app/book": {"get": {"tags": ["Book"], "parameters": [{"name": "Sorting", "in": "query", "schema": {"type": "string"}}, {"name": "SkipCount", "in": "query", "schema": {"maximum": 2147483647, "minimum": 0, "type": "integer", "format": "int32"}}, {"name": "MaxResultCount", "in": "query", "schema": {"maximum": 2147483647, "minimum": 1, "type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Application.Dtos.PagedResultDto`1[[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Application.Dtos.PagedResultDto`1[[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Application.Dtos.PagedResultDto`1[[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}, "post": {"tags": ["Book"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/api/app/book/hello-world": {"get": {"tags": ["Book"], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/api/app/book/{id}": {"put": {"tags": ["Book"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}, "delete": {"tags": ["Book"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}, "get": {"tags": ["Book"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "501": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorResponse"}}}}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pe.WebApi.WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pe.WebApi.WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pe.WebApi.WeatherForecast"}}}}}}}}}, "components": {"schemas": {"Pe.Application.Contracts.Dtos.BookDto": {"type": "object", "additionalProperties": false}, "Pe.WebApi.WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Application.Dtos.PagedResultDto`1[[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Pe.Application.Contracts.Dtos.BookDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto": {"type": "object", "properties": {"grantedPolicies": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto": {"type": "object", "properties": {"localization": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto"}, "auth": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationAuthConfigurationDto"}, "setting": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto"}, "currentUser": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto"}, "features": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto"}, "globalFeatures": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto"}, "multiTenancy": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto"}, "currentTenant": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto"}, "timing": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto"}, "clock": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto"}, "objectExtensions": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto"}, "extraProperties": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationFeatureConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationGlobalFeatureConfigurationDto": {"type": "object", "properties": {"enabledFeatures": {"uniqueItems": true, "type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": {"type": "string"}}, "nullable": true}, "resources": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto"}, "nullable": true}, "languages": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Localization.LanguageInfo"}, "nullable": true}, "currentCulture": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto"}, "defaultResourceName": {"type": "string", "nullable": true}, "languagesMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.NameValue"}}, "nullable": true}, "languageFilesMap": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.NameValue"}}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto": {"type": "object", "properties": {"resources": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto"}, "nullable": true}, "currentCulture": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationResourceDto": {"type": "object", "properties": {"texts": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "baseResources": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationSettingConfigurationDto": {"type": "object", "properties": {"values": {"type": "object", "additionalProperties": {"type": "string", "nullable": true}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ClockDto": {"type": "object", "properties": {"kind": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentCultureDto": {"type": "object", "properties": {"displayName": {"type": "string", "nullable": true}, "englishName": {"type": "string", "nullable": true}, "threeLetterIsoLanguageName": {"type": "string", "nullable": true}, "twoLetterIsoLanguageName": {"type": "string", "nullable": true}, "isRightToLeft": {"type": "boolean"}, "cultureName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "nativeName": {"type": "string", "nullable": true}, "dateTimeFormat": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.CurrentUserDto": {"type": "object", "properties": {"isAuthenticated": {"type": "boolean"}, "id": {"type": "string", "format": "uuid", "nullable": true}, "tenantId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorUserId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorTenantId": {"type": "string", "format": "uuid", "nullable": true}, "impersonatorUserName": {"type": "string", "nullable": true}, "impersonatorTenantName": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "surName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "emailVerified": {"type": "boolean"}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberVerified": {"type": "boolean"}, "roles": {"type": "array", "items": {"type": "string"}, "nullable": true}, "sessionId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.DateTimeFormatDto": {"type": "object", "properties": {"calendarAlgorithmType": {"type": "string", "nullable": true}, "dateTimeFormatLong": {"type": "string", "nullable": true}, "shortDatePattern": {"type": "string", "nullable": true}, "fullDateTimePattern": {"type": "string", "nullable": true}, "dateSeparator": {"type": "string", "nullable": true}, "shortTimePattern": {"type": "string", "nullable": true}, "longTimePattern": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone": {"type": "object", "properties": {"timeZoneName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto": {"type": "object", "properties": {"properties": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto": {"type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto"}, "nullable": true}, "localizationResource": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumFieldDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto": {"type": "object", "properties": {"onGet": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto"}, "onCreate": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiCreateDto"}, "onUpdate": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiGetDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiUpdateDto": {"type": "object", "properties": {"isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto": {"type": "object", "properties": {"typeSimple": {"type": "string", "nullable": true}, "config": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyDto": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "displayName": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto"}, "api": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyApiDto"}, "ui": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto"}, "attributes": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyAttributeDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}, "defaultValue": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiDto": {"type": "object", "properties": {"onTable": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto"}, "onCreateForm": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto"}, "onEditForm": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto"}, "lookup": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiFormDto": {"type": "object", "properties": {"isVisible": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiLookupDto": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "resultListPropertyName": {"type": "string", "nullable": true}, "displayPropertyName": {"type": "string", "nullable": true}, "valuePropertyName": {"type": "string", "nullable": true}, "filterParamName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionPropertyUiTableDto": {"type": "object", "properties": {"isVisible": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.LocalizableStringDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "resource": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto": {"type": "object", "properties": {"entities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.EntityExtensionDto"}, "nullable": true}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ObjectExtensionsDto": {"type": "object", "properties": {"modules": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ModuleExtensionDto"}, "nullable": true}, "enums": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ObjectExtending.ExtensionEnumDto"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone": {"type": "object", "properties": {"iana": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.IanaTimeZone"}, "windows": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimingDto": {"type": "object", "properties": {"timeZone": {"$ref": "#/components/schemas/Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.TimeZone"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.WindowsTimeZone": {"type": "object", "properties": {"timeZoneId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.CurrentTenantDto": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "nullable": true}, "name": {"type": "string", "nullable": true}, "isAvailable": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.AspNetCore.Mvc.MultiTenancy.MultiTenancyInfoDto": {"type": "object", "properties": {"isEnabled": {"type": "boolean"}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ActionApiDescriptionModel": {"type": "object", "properties": {"uniqueName": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "httpMethod": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "supportedVersions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "parametersOnMethod": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel"}, "nullable": true}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ParameterApiDescriptionModel"}, "nullable": true}, "returnValue": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel"}, "allowAnonymous": {"type": "boolean", "nullable": true}, "implementFrom": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel": {"type": "object", "properties": {"modules": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ModuleApiDescriptionModel"}, "nullable": true}, "types": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.TypeApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ControllerApiDescriptionModel": {"type": "object", "properties": {"controllerName": {"type": "string", "nullable": true}, "controllerGroupName": {"type": "string", "nullable": true}, "isRemoteService": {"type": "boolean"}, "isIntegrationService": {"type": "boolean"}, "apiVersion": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "interfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel"}, "nullable": true}, "actions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ActionApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ControllerInterfaceApiDescriptionModel": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "methods": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.InterfaceMethodApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "parametersOnMethod": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel"}, "nullable": true}, "returnValue": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel"}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.MethodParameterApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "typeAsString": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isOptional": {"type": "boolean"}, "defaultValue": {"nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ModuleApiDescriptionModel": {"type": "object", "properties": {"rootPath": {"type": "string", "nullable": true}, "remoteServiceName": {"type": "string", "nullable": true}, "controllers": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.ControllerApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ParameterApiDescriptionModel": {"type": "object", "properties": {"nameOnMethod": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "jsonName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isOptional": {"type": "boolean"}, "defaultValue": {"nullable": true}, "constraintTypes": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bindingSourceId": {"type": "string", "nullable": true}, "descriptorName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.PropertyApiDescriptionModel": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "jsonName": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}, "isRequired": {"type": "boolean"}, "minLength": {"type": "integer", "format": "int32", "nullable": true}, "maxLength": {"type": "integer", "format": "int32", "nullable": true}, "minimum": {"type": "string", "nullable": true}, "maximum": {"type": "string", "nullable": true}, "regex": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.ReturnValueApiDescriptionModel": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "typeSimple": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.Modeling.TypeApiDescriptionModel": {"type": "object", "properties": {"baseType": {"type": "string", "nullable": true}, "isEnum": {"type": "boolean"}, "enumNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "enumValues": {"type": "array", "items": {}, "nullable": true}, "genericArguments": {"type": "array", "items": {"type": "string"}, "nullable": true}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.Modeling.PropertyApiDescriptionModel"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.RemoteServiceErrorInfo": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "details": {"type": "string", "nullable": true}, "data": {"type": "object", "additionalProperties": {}, "nullable": true}, "validationErrors": {"type": "array", "items": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceValidationErrorInfo"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Http.RemoteServiceErrorResponse": {"type": "object", "properties": {"error": {"$ref": "#/components/schemas/Volo.Abp.Http.RemoteServiceErrorInfo"}}, "additionalProperties": false}, "Volo.Abp.Http.RemoteServiceValidationErrorInfo": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "members": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Volo.Abp.Localization.LanguageInfo": {"type": "object", "properties": {"cultureName": {"type": "string", "nullable": true}, "uiCultureName": {"type": "string", "nullable": true}, "displayName": {"type": "string", "nullable": true}, "twoLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "Volo.Abp.NameValue": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}