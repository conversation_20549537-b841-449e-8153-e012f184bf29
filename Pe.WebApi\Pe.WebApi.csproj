﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc" Version="8.3.4" />
    <PackageReference Include="Volo.Abp.Autofac" Version="8.3.4" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="8.3.4" />
    <PackageReference Include="Volo.Abp.EntityFrameworkCore" Version="8.3.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Pe.Application\Pe.Application.csproj" />
    <ProjectReference Include="..\Pe.SqlSugarCore\Pe.SqlSugarCore.csproj" />
  </ItemGroup>

</Project>
