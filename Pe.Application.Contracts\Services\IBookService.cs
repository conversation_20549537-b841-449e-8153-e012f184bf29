using Pe.Application.Contracts.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace Pe.Application.Contracts.Services;

public interface IBookService : ICrudAppService<BookDto, Guid>
{
    string GetHelloWorld();

    /// <summary>
    /// 创建新书籍
    /// </summary>
    Task<BookDto> CreateBookAsync(CreateBookDto input);

    /// <summary>
    /// 根据名称搜索书籍
    /// </summary>
    Task<List<BookDto>> SearchBooksByNameAsync(string name);

    /// <summary>
    /// 分页查询书籍
    /// </summary>
    Task<PagedResultDto<BookDto>> GetPagedBooksAsync(GetBooksInput input);
}
