﻿using Microsoft.Extensions.DependencyInjection;
using Pe.Domain.Entities;
using SqlSugar;
using Volo.Abp;
using Volo.Abp.Modularity;

namespace Pe.SqlSugarCore;

public class PeSqlSugarCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var cfg = new ConnectionConfig()
        {
            ConnectionString = "Data Source=8.134.137.199;Initial Catalog=pre_pe;User ID=sa;Password=*********;",
            DbType = DbType.SqlServer,
            IsAutoCloseConnection = true
        };

        context.Services.AddScoped<ISqlSugarClient>(_ => new SqlSugarClient(cfg));
    }

    public override void OnPostApplicationInitialization(ApplicationInitializationContext context)
    {
        var db = context.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        db.CodeFirst.InitTables(typeof(BookAggregateRoot));
    }
}
