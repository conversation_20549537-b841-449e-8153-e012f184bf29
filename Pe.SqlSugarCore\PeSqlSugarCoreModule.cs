using Microsoft.Extensions.DependencyInjection;
using Pe.Domain;
using Pe.Domain.Entities;
using Pe.SqlSugarCore.Repositories;
using SqlSugar;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Modularity;

namespace Pe.SqlSugarCore;

[DependsOn(typeof(PeDomainModule))]
public class PeSqlSugarCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var cfg = new ConnectionConfig()
        {
            ConnectionString = "Data Source=8.134.137.199;Initial Catalog=pre_pe;User ID=sa;Password=*********;",
            DbType = DbType.SqlServer,
            IsAutoCloseConnection = true
        };

        // 注册 SqlSugar 客户端
        context.Services.AddScoped<ISqlSugarClient>(_ => new SqlSugarClient(cfg));

        // 注册 Repository
        context.Services.AddScoped<IRepository<BookAggregateRoot, Guid>, BookRepository>();
        context.Services.AddScoped<BookRepository>();
    }

    public override void OnPostApplicationInitialization(ApplicationInitializationContext context)
    {
        var db = context.ServiceProvider.GetRequiredService<ISqlSugarClient>();
        db.CodeFirst.InitTables(typeof(BookAggregateRoot));
    }
}
