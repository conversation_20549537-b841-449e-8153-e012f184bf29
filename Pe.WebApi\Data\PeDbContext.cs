using Microsoft.EntityFrameworkCore;
using Pe.Domain.Entities;
using Volo.Abp.Data;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace Pe.WebApi.Data;

[ConnectionStringName("Default")]
public class PeDbContext : AbpDbContext<PeDbContext>
{
    public DbSet<BookAggregateRoot> Books { get; set; }

    public PeDbContext(DbContextOptions<PeDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.Entity<BookAggregateRoot>(b =>
        {
            b.ToTable("Books");
            b.ConfigureByConvention();
        });
    }
}
