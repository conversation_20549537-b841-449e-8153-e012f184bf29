﻿using Microsoft.EntityFrameworkCore;
using Pe.Application;
using Pe.WebApi.Data;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.Autofac;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Modularity;
using Volo.Abp.Swashbuckle;

namespace Pe.WebApi;

[DependsOn(
    typeof(PeApplicationModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAutofacModule),
    typeof(AbpEntityFrameworkCoreModule)
)]
public class PeWebApiModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var services = context.Services;

        // Configure Entity Framework Core
        services.AddAbpDbContext<PeDbContext>(options =>
        {
            options.AddDefaultRepositories(includeAllEntities: true);
        });

        Configure<AbpDbContextOptions>(options =>
        {
            options.Configure<PeDbContext>(context =>
            {
                context.DbContextOptions.UseInMemoryDatabase("TestDb");
            });
        });

        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.AutoValidate = false;
        });

        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(PeApplicationModule).Assembly);
        });
    }
}
