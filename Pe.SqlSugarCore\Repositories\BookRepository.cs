using Pe.Domain.Entities;
using SqlSugar;
using Volo.Abp.Domain.Repositories;

namespace Pe.SqlSugarCore.Repositories;

public class BookRepository : SqlSugarRepositoryBase<BookAggregateRoot, Guid>, IRepository<BookAggregateRoot, Guid>
{
    public BookRepository(ISqlSugarClient db) : base(db)
    {
    }

    /// <summary>
    /// 根据名称查询书籍
    /// </summary>
    public async Task<List<BookAggregateRoot>> GetBooksByNameAsync(string name)
    {
        return await _db.Queryable<BookAggregateRoot>()
            .Where(x => x.Name != null && x.Name.Contains(name))
            .ToListAsync();
    }

    /// <summary>
    /// 分页查询书籍
    /// </summary>
    public async Task<(List<BookAggregateRoot> Items, int TotalCount)> GetPagedBooksAsync(
        int pageIndex, 
        int pageSize, 
        string? searchName = null)
    {
        var query = _db.Queryable<BookAggregateRoot>();

        if (!string.IsNullOrEmpty(searchName))
        {
            query = query.Where(x => x.Name != null && x.Name.Contains(searchName));
        }

        var totalCount = await query.CountAsync();
        var items = await query
            .OrderBy(x => x.CreationTime, OrderByType.Desc)
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (items, totalCount);
    }
}
