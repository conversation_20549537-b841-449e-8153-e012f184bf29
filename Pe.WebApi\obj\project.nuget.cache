{"version": 2, "dgSpecHash": "k/Px0YAMQW8=", "success": true, "projectFilePath": "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.abstractions\\8.1.0\\asp.versioning.abstractions.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.http\\8.1.0\\asp.versioning.http.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc\\8.1.0\\asp.versioning.mvc.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asp.versioning.mvc.apiexplorer\\8.1.0\\asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\asynckeyedlock\\6.3.4\\asynckeyedlock.6.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac\\8.0.0\\autofac.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extensions.dependencyinjection\\9.0.0\\autofac.extensions.dependencyinjection.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\autofac.extras.dynamicproxy\\7.1.0\\autofac.extras.dynamicproxy.7.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper\\13.0.1\\automapper.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core\\5.1.1\\castle.core.5.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\castle.core.asyncinterceptor\\2.1.0\\castle.core.asyncinterceptor.2.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\devicedetector.net\\6.1.4\\devicedetector.net.6.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\identitymodel\\6.2.0\\identitymodel.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2023.3.0\\jetbrains.annotations.2023.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\litedb\\5.0.17\\litedb.5.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.openidconnect\\8.0.4\\microsoft.aspnetcore.authentication.openidconnect.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\8.0.4\\microsoft.aspnetcore.authorization.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\8.0.4\\microsoft.aspnetcore.metadata.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.extensions\\6.0.0\\microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.mvc.razor.runtimecompilation\\8.0.4\\microsoft.aspnetcore.mvc.razor.runtimecompilation.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.razor.language\\6.0.0\\microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\8.0.0\\microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.2\\microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.0.0\\microsoft.codeanalysis.common.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.0.0\\microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.razor\\6.0.0\\microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\microsoft.extensions.configuration.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\8.0.0\\microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\8.0.0\\microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\8.0.0\\microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.0\\microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.0\\microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\8.0.0\\microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.0\\microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.1\\microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.0\\microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\8.0.0\\microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.composite\\8.0.0\\microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.embedded\\8.0.4\\microsoft.extensions.fileproviders.embedded.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\8.0.0\\microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\8.0.0\\microsoft.extensions.localization.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\8.0.0\\microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.0\\microsoft.extensions.logging.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.1\\microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\microsoft.extensions.options.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\8.0.0\\microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\microsoft.extensions.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.1.2\\microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.1.2\\microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.1.2\\microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.1.2\\microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.1.2\\microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.1.2\\microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.0\\microsoft.netcore.platforms.3.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.2.3\\microsoft.openapi.1.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.context\\5.1.2\\nito.asyncex.context.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.asyncex.tasks\\5.1.2\\nito.asyncex.tasks.5.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nito.disposables\\2.2.1\\nito.disposables.2.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuglify\\1.21.0\\nuglify.1.21.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sqlsugar\\5.1.4.157\\sqlsugar.5.1.4.157.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\6.5.0\\swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\6.5.0\\swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\6.5.0\\swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\6.5.0\\swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\8.0.0\\system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\6.0.0\\system.diagnostics.eventlog.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.1.2\\system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.dynamic.core\\1.3.5\\system.linq.dynamic.core.1.3.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.queryable\\4.3.0\\system.linq.queryable.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\5.0.0\\system.reflection.metadata.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\4.7.0\\system.security.accesscontrol.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\4.5.1\\system.text.encoding.codepages.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\8.0.0\\system.text.encodings.web.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\8.0.4\\system.text.json.8.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\timezoneconverter\\6.1.0\\timezoneconverter.6.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.apiversioning.abstractions\\8.3.4\\volo.abp.apiversioning.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore\\8.3.4\\volo.abp.aspnetcore.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.abstractions\\8.3.4\\volo.abp.aspnetcore.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc\\8.3.4\\volo.abp.aspnetcore.mvc.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.aspnetcore.mvc.contracts\\8.3.4\\volo.abp.aspnetcore.mvc.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing\\8.3.4\\volo.abp.auditing.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.auditing.contracts\\8.3.4\\volo.abp.auditing.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization\\8.3.4\\volo.abp.authorization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.authorization.abstractions\\8.3.4\\volo.abp.authorization.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.autofac\\8.3.4\\volo.abp.autofac.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.automapper\\8.3.4\\volo.abp.automapper.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.backgroundworkers\\8.3.4\\volo.abp.backgroundworkers.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.caching\\8.3.4\\volo.abp.caching.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.castle.core\\8.3.4\\volo.abp.castle.core.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.core\\8.3.4\\volo.abp.core.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.data\\8.3.4\\volo.abp.data.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application\\8.3.4\\volo.abp.ddd.application.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.application.contracts\\8.3.4\\volo.abp.ddd.application.contracts.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain\\8.3.4\\volo.abp.ddd.domain.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ddd.domain.shared\\8.3.4\\volo.abp.ddd.domain.shared.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.distributedlocking.abstractions\\8.3.4\\volo.abp.distributedlocking.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus\\8.3.4\\volo.abp.eventbus.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.eventbus.abstractions\\8.3.4\\volo.abp.eventbus.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.exceptionhandling\\8.3.4\\volo.abp.exceptionhandling.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.features\\8.3.4\\volo.abp.features.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.globalfeatures\\8.3.4\\volo.abp.globalfeatures.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.guids\\8.3.4\\volo.abp.guids.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http\\8.3.4\\volo.abp.http.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.http.abstractions\\8.3.4\\volo.abp.http.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json\\8.3.4\\volo.abp.json.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.abstractions\\8.3.4\\volo.abp.json.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.json.systemtextjson\\8.3.4\\volo.abp.json.systemtextjson.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization\\8.3.4\\volo.abp.localization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.localization.abstractions\\8.3.4\\volo.abp.localization.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.minify\\8.3.4\\volo.abp.minify.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy\\8.3.4\\volo.abp.multitenancy.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.multitenancy.abstractions\\8.3.4\\volo.abp.multitenancy.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectextending\\8.3.4\\volo.abp.objectextending.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.objectmapping\\8.3.4\\volo.abp.objectmapping.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.security\\8.3.4\\volo.abp.security.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.serialization\\8.3.4\\volo.abp.serialization.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.settings\\8.3.4\\volo.abp.settings.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.specifications\\8.3.4\\volo.abp.specifications.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.swashbuckle\\8.3.4\\volo.abp.swashbuckle.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.threading\\8.3.4\\volo.abp.threading.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.timing\\8.3.4\\volo.abp.timing.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui\\8.3.4\\volo.abp.ui.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.ui.navigation\\8.3.4\\volo.abp.ui.navigation.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.uow\\8.3.4\\volo.abp.uow.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation\\8.3.4\\volo.abp.validation.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.validation.abstractions\\8.3.4\\volo.abp.validation.abstractions.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\volo.abp.virtualfilesystem\\8.3.4\\volo.abp.virtualfilesystem.8.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\yamldotnet\\13.1.1\\yamldotnet.13.1.1.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net8.0”还原包“SqlSugar 5.1.4.157”。此包可能与项目不完全兼容。", "libraryId": "SqlSugar", "targetGraphs": ["net8.0"]}]}