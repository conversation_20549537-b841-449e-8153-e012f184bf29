using Pe.Application.Contracts.Dtos;
using Pe.Domain.Entities;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Pe.Application;

public class BookService : CrudAppService<BookAggregateRoot, BookDto, Guid>
{
    public BookService(IRepository<BookAggregateRoot, Guid> repository) : base(repository)
    {
    }

    public override async Task<PagedResultDto<BookDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var books = await Repository.GetPagedListAsync(input.SkipCount, input.MaxResultCount, input.Sorting ?? "");
        var dtos = await MapToGetListOutputDtosAsync(books);

        return new PagedResultDto<BookDto>(dtos.Count, dtos);
    }

    public string GetHelloWorld()
    {
        return "Hello World!";
    }
}
