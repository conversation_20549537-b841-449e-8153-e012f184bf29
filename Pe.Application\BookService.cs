using Pe.Application.Contracts.Dtos;
using Pe.Application.Contracts.Services;
using Pe.Domain.Entities;
using Pe.SqlSugarCore.Repositories;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Pe.Application;

public class BookService : CrudAppService<BookAggregateRoot, BookDto, Guid>, IBookService
{
    private readonly BookRepository _bookRepository;

    public BookService(IRepository<BookAggregateRoot, Guid> repository, BookRepository bookRepository) : base(repository)
    {
        _bookRepository = bookRepository;
    }

    public override async Task<PagedResultDto<BookDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        var books = await Repository.GetPagedListAsync(input.SkipCount, input.MaxResultCount, input.Sorting ?? "");
        var dtos = await MapToGetListOutputDtosAsync(books);

        return new PagedResultDto<BookDto>(dtos.Count, dtos);
    }

    public string GetHelloWorld()
    {
        return "Hello World!";
    }

    /// <summary>
    /// 创建新书籍
    /// </summary>
    public async Task<BookDto> CreateBookAsync(CreateBookDto input)
    {
        var book = new BookAggregateRoot(GuidGenerator.Create())
        {
            Name = input.Name
        };

        var createdBook = await Repository.InsertAsync(book, autoSave: true);
        return await MapToGetOutputDtoAsync(createdBook);
    }

    /// <summary>
    /// 根据名称搜索书籍
    /// </summary>
    public async Task<List<BookDto>> SearchBooksByNameAsync(string name)
    {
        var books = await _bookRepository.GetBooksByNameAsync(name);
        return await MapToGetListOutputDtosAsync(books);
    }

    /// <summary>
    /// 分页查询书籍
    /// </summary>
    public async Task<PagedResultDto<BookDto>> GetPagedBooksAsync(GetBooksInput input)
    {
        var (items, totalCount) = await _bookRepository.GetPagedBooksAsync(
            input.SkipCount / input.MaxResultCount + 1,
            input.MaxResultCount,
            input.SearchName);

        var dtos = await MapToGetListOutputDtosAsync(items);
        return new PagedResultDto<BookDto>(totalCount, dtos);
    }
}
