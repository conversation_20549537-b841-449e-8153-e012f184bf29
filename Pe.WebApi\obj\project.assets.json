{"version": 3, "targets": {"net8.0": {"Asp.Versioning.Abstractions/8.1.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Abstractions.dll": {"related": ".xml"}}}, "Asp.Versioning.Http/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Abstractions": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Http.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Asp.Versioning.Mvc/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Http": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.dll": {"related": ".xml"}}}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"type": "package", "dependencies": {"Asp.Versioning.Mvc": "8.1.0"}, "compile": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll": {"related": ".xml"}}}, "AsyncKeyedLock/6.3.4": {"type": "package", "compile": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"related": ".xml"}}}, "Autofac/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "7.0.2"}, "compile": {"lib/net8.0/Autofac.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Autofac.dll": {"related": ".xml"}}}, "Autofac.Extensions.DependencyInjection/9.0.0": {"type": "package", "dependencies": {"Autofac": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Autofac.Extensions.DependencyInjection.dll": {"related": ".xml"}}}, "Autofac.Extras.DynamicProxy/7.1.0": {"type": "package", "dependencies": {"Autofac": "6.5.0", "Castle.Core": "5.1.1"}, "compile": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Autofac.Extras.DynamicProxy.dll": {"related": ".xml"}}}, "AutoMapper/13.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.Options": "6.0.0"}, "compile": {"lib/net6.0/AutoMapper.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/AutoMapper.dll": {"related": ".xml"}}}, "Castle.Core/5.1.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "compile": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.dll": {"related": ".xml"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "dependencies": {"Castle.Core": "4.4.0"}, "compile": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"related": ".xml"}}}, "DeviceDetector.NET/6.1.4": {"type": "package", "dependencies": {"LiteDB": "5.0.17", "Microsoft.Extensions.Logging.Abstractions": "7.0.1", "Newtonsoft.Json": "13.0.3", "YamlDotNet": "13.1.1"}, "compile": {"lib/net7.0/DeviceDetector.NET.dll": {}}, "runtime": {"lib/net7.0/DeviceDetector.NET.dll": {}}}, "IdentityModel/6.2.0": {"type": "package", "compile": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/IdentityModel.dll": {"related": ".pdb;.xml"}}}, "JetBrains.Annotations/2023.3.0": {"type": "package", "compile": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}, "runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"related": ".deps.json;.xml"}}}, "LiteDB/5.0.17": {"type": "package", "compile": {"lib/netstandard2.0/LiteDB.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/LiteDB.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.4": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.AspNetCore.Authorization/8.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.4", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Metadata/8.0.4": {"type": "package", "compile": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"related": ".xml"}}}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Extensions": "6.0.0", "Microsoft.CodeAnalysis.Razor": "6.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "build": {"buildTransitive/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets": {}}}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.CodeAnalysis.Common/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.2", "System.Collections.Immutable": "5.0.0", "System.Memory": "4.5.4", "System.Reflection.Metadata": "5.0.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Text.Encoding.CodePages": "4.5.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[4.0.0]"}, "compile": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.0", "Microsoft.CodeAnalysis.CSharp": "4.0.0", "Microsoft.CodeAnalysis.Common": "4.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"related": ".xml"}}, "build": {"build/netstandard2.0/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"related": ".xml"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "dependencies": {"Nito.Disposables": "2.2.1"}, "compile": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"related": ".xml"}}}, "Nito.Disposables/2.2.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.7.1"}, "compile": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"related": ".xml"}}}, "NUglify/1.21.0": {"type": "package", "compile": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/NUglify.dll": {"related": ".xml"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "SqlSugar/*********": {"type": "package", "compile": {"lib/SqlSugar.dll": {}}, "runtime": {"lib/SqlSugar.dll": {}}}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}, "build": {"build/_._": {}}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "compile": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"related": ".pdb;.xml"}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Queryable/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Linq.Queryable.dll": {}}}, "System.Memory/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Emit/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.1/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Reflection.Metadata/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Reflection.Metadata.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Loader/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Security.AccessControl/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.CodePages/4.5.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "2.1.2", "System.Runtime.CompilerServices.Unsafe": "4.5.2"}, "compile": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.4": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "TimeZoneConverter/6.1.0": {"type": "package", "compile": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/TimeZoneConverter.dll": {"related": ".xml"}}}, "Volo.Abp.ApiVersioning.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore/8.3.4": {"type": "package", "dependencies": {"DeviceDetector.NET": "6.1.4", "IdentityModel": "6.2.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.4", "Volo.Abp.AspNetCore.Abstractions": "8.3.4", "Volo.Abp.Auditing": "8.3.4", "Volo.Abp.Authorization": "8.3.4", "Volo.Abp.ExceptionHandling": "8.3.4", "Volo.Abp.Http": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Uow": "8.3.4", "Volo.Abp.Validation": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"], "contentFiles": {"contentFiles/any/any/_._": {"buildAction": "None", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.AspNetCore.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AspNetCore.Mvc/8.3.4": {"type": "package", "dependencies": {"Asp.Versioning.Mvc": "8.1.0", "Asp.Versioning.Mvc.ApiExplorer": "8.1.0", "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": "8.0.4", "Volo.Abp.ApiVersioning.Abstractions": "8.3.4", "Volo.Abp.AspNetCore": "8.3.4", "Volo.Abp.AspNetCore.Mvc.Contracts": "8.3.4", "Volo.Abp.Ddd.Application": "8.3.4", "Volo.Abp.GlobalFeatures": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.UI.Navigation": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll": {"related": ".pdb;.xml"}}, "contentFiles": {"contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json": {"buildAction": "Content", "codeLanguage": "any", "copyToOutput": false}}}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Ddd.Application.Contracts": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Auditing/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing.Contracts": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Threading": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Auditing.Contracts/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Security": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Authorization.Abstractions/8.3.4": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Autofac/8.3.4": {"type": "package", "dependencies": {"Autofac": "8.0.0", "Autofac.Extensions.DependencyInjection": "9.0.0", "Autofac.Extras.DynamicProxy": "7.1.0", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Volo.Abp.Castle.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Autofac.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Autofac.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.AutoMapper/8.3.4": {"type": "package", "dependencies": {"AutoMapper": "13.0.1", "Volo.Abp.Auditing": "8.3.4", "Volo.Abp.ObjectExtending": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.AutoMapper.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.BackgroundWorkers/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Threading": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Caching/8.3.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Serialization": "8.3.4", "Volo.Abp.Threading": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Castle.Core/8.3.4": {"type": "package", "dependencies": {"Castle.Core": "5.1.1", "Castle.Core.AsyncInterceptor": "2.1.0", "Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Castle.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Core/8.3.4": {"type": "package", "dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Data/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.ObjectExtending": "8.3.4", "Volo.Abp.Uow": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "8.3.4", "Volo.Abp.Ddd.Application.Contracts": "8.3.4", "Volo.Abp.Ddd.Domain": "8.3.4", "Volo.Abp.Features": "8.3.4", "Volo.Abp.GlobalFeatures": "8.3.4", "Volo.Abp.Http.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Settings": "8.3.4", "Volo.Abp.Validation": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Application.Contracts/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing.Contracts": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Auditing": "8.3.4", "Volo.Abp.Caching": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Ddd.Domain.Shared": "8.3.4", "Volo.Abp.EventBus": "8.3.4", "Volo.Abp.ExceptionHandling": "8.3.4", "Volo.Abp.Guids": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4", "Volo.Abp.Specifications": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Ddd.Domain.Shared/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.3.4": {"type": "package", "dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.BackgroundWorkers": "8.3.4", "Volo.Abp.DistributedLocking.Abstractions": "8.3.4", "Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.Guids": "8.3.4", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.EventBus.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.ObjectExtending": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ExceptionHandling/8.3.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Features/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Validation": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.GlobalFeatures/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Guids/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Http.Abstractions": "8.3.4", "Volo.Abp.Json": "8.3.4", "Volo.Abp.Minify": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Http.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Json.SystemTextJson": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Json.SystemTextJson/8.3.4": {"type": "package", "dependencies": {"System.Text.Json": "8.0.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Json.Abstractions": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Settings": "8.3.4", "Volo.Abp.Threading": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Localization.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Minify/8.3.4": {"type": "package", "dependencies": {"NUglify": "1.21.0", "Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Minify.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.3.4", "Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Settings": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectExtending/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Validation.Abstractions": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.ObjectMapping/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Security/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Serialization/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Settings/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Security": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Specifications/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Swashbuckle/8.3.4": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore": "6.5.0", "Volo.Abp.AspNetCore.Mvc": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Swashbuckle.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Swashbuckle.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Threading/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Timing/8.3.4": {"type": "package", "dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.Settings": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.ExceptionHandling": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.UI.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.UI.Navigation/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Authorization": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.UI": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.UI.Navigation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Uow/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Localization": "8.3.4", "Volo.Abp.Validation.Abstractions": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.Validation.Abstractions/8.3.4": {"type": "package", "dependencies": {"Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"related": ".pdb;.xml"}}}, "Volo.Abp.VirtualFileSystem/8.3.4": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.4", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.3.4"}, "compile": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"related": ".pdb;.xml"}}}, "YamlDotNet/13.1.1": {"type": "package", "compile": {"lib/net7.0/YamlDotNet.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/YamlDotNet.dll": {"related": ".xml"}}}, "Pe.Application/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Pe.Application.Contracts": "1.0.0", "Pe.Domain": "1.0.0", "Pe.SqlSugarCore": "1.0.0", "Volo.Abp.AutoMapper": "8.3.4", "Volo.Abp.Ddd.Application": "8.3.4"}, "compile": {"bin/placeholder/Pe.Application.dll": {}}, "runtime": {"bin/placeholder/Pe.Application.dll": {}}}, "Pe.Application.Contracts/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Pe.Domain.Shared": "1.0.0", "Volo.Abp.Ddd.Application.Contracts": "8.3.4"}, "compile": {"bin/placeholder/Pe.Application.Contracts.dll": {}}, "runtime": {"bin/placeholder/Pe.Application.Contracts.dll": {}}}, "Pe.Domain/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Pe.Domain.Shared": "1.0.0", "Volo.Abp.Ddd.Domain": "8.3.4"}, "compile": {"bin/placeholder/Pe.Domain.dll": {}}, "runtime": {"bin/placeholder/Pe.Domain.dll": {}}}, "Pe.Domain.Shared/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Volo.Abp.Ddd.Domain.Shared": "8.3.4"}, "compile": {"bin/placeholder/Pe.Domain.Shared.dll": {}}, "runtime": {"bin/placeholder/Pe.Domain.Shared.dll": {}}}, "Pe.SqlSugarCore/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Pe.Domain": "1.0.0", "SqlSugar": "*********", "System.Data.SqlClient": "4.8.6", "Volo.Abp.Ddd.Domain": "8.3.4"}, "compile": {"bin/placeholder/Pe.SqlSugarCore.dll": {}}, "runtime": {"bin/placeholder/Pe.SqlSugarCore.dll": {}}}}}, "libraries": {"Asp.Versioning.Abstractions/8.1.0": {"sha512": "mpeNZyMdvrHztJwR1sXIUQ+3iioEU97YMBnFA9WLbsPOYhGwDJnqJMmEd8ny7kcmS9OjTHoEuX/bSXXY3brIFA==", "type": "package", "path": "asp.versioning.abstractions/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.abstractions.8.1.0.nupkg.sha512", "asp.versioning.abstractions.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Abstractions.dll", "lib/net8.0/Asp.Versioning.Abstractions.xml", "lib/netstandard1.0/Asp.Versioning.Abstractions.dll", "lib/netstandard1.0/Asp.Versioning.Abstractions.xml", "lib/netstandard2.0/Asp.Versioning.Abstractions.dll", "lib/netstandard2.0/Asp.Versioning.Abstractions.xml"]}, "Asp.Versioning.Http/8.1.0": {"sha512": "Xu4xF62Cu9JqYi/CTa2TiK5kyHoa4EluPynj/bPFWDmlTIPzuJQbBI5RgFYVRFHjFVvWMoA77acRaFu7i7Wzqg==", "type": "package", "path": "asp.versioning.http/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.http.8.1.0.nupkg.sha512", "asp.versioning.http.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Http.dll", "lib/net8.0/Asp.Versioning.Http.xml"]}, "Asp.Versioning.Mvc/8.1.0": {"sha512": "BMAJM2sGsTUw5FQ9upKQt6GFoldWksePgGpYjl56WSRvIuE3UxKZh0gAL+wDTIfLshUZm97VCVxlOGyrcjWz9Q==", "type": "package", "path": "asp.versioning.mvc/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.mvc.8.1.0.nupkg.sha512", "asp.versioning.mvc.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Mvc.dll", "lib/net8.0/Asp.Versioning.Mvc.xml"]}, "Asp.Versioning.Mvc.ApiExplorer/8.1.0": {"sha512": "a90gW/4TF/14Bjiwg9LqNtdKGC4G3gu02+uynq3bCISfQm48km5chny4Yg5J4hixQPJUwwJJ9Do1G+jM8L9h3g==", "type": "package", "path": "asp.versioning.mvc.apiexplorer/8.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "asp.versioning.mvc.apiexplorer.8.1.0.nupkg.sha512", "asp.versioning.mvc.apiexplorer.nuspec", "icon.png", "lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.dll", "lib/net8.0/Asp.Versioning.Mvc.ApiExplorer.xml"]}, "AsyncKeyedLock/6.3.4": {"sha512": "+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "type": "package", "path": "asynckeyedlock/6.3.4", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "asynckeyedlock.6.3.4.nupkg.sha512", "asynckeyedlock.nuspec", "lib/net5.0/AsyncKeyedLock.dll", "lib/net5.0/AsyncKeyedLock.xml", "lib/net8.0/AsyncKeyedLock.dll", "lib/net8.0/AsyncKeyedLock.xml", "lib/netstandard2.0/AsyncKeyedLock.dll", "lib/netstandard2.0/AsyncKeyedLock.xml", "lib/netstandard2.1/AsyncKeyedLock.dll", "lib/netstandard2.1/AsyncKeyedLock.xml", "logo.png"]}, "Autofac/8.0.0": {"sha512": "qxVqJcl3fixxa5aZc9TmIuYTwooI9GCL5RzfUiTZtTlbAF3NcWz7bPeEyJEAyS/0qGhSyGnXeku2eiu/7L+3qw==", "type": "package", "path": "autofac/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.8.0.0.nupkg.sha512", "autofac.nuspec", "icon.png", "lib/net6.0/Autofac.dll", "lib/net6.0/Autofac.xml", "lib/net7.0/Autofac.dll", "lib/net7.0/Autofac.xml", "lib/net8.0/Autofac.dll", "lib/net8.0/Autofac.xml", "lib/netstandard2.0/Autofac.dll", "lib/netstandard2.0/Autofac.xml", "lib/netstandard2.1/Autofac.dll", "lib/netstandard2.1/Autofac.xml"]}, "Autofac.Extensions.DependencyInjection/9.0.0": {"sha512": "tf+//4MBola256qaaVQqQ6tx2R57S8A8BFekRWNpHkpSFzRBPkU+/fEDUSrCjqldK/B2zRoDbsMcQmYy3PYGWg==", "type": "package", "path": "autofac.extensions.dependencyinjection/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.extensions.dependencyinjection.9.0.0.nupkg.sha512", "autofac.extensions.dependencyinjection.nuspec", "icon.png", "lib/net6.0/Autofac.Extensions.DependencyInjection.dll", "lib/net6.0/Autofac.Extensions.DependencyInjection.xml", "lib/net7.0/Autofac.Extensions.DependencyInjection.dll", "lib/net7.0/Autofac.Extensions.DependencyInjection.xml", "lib/net8.0/Autofac.Extensions.DependencyInjection.dll", "lib/net8.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Autofac.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Autofac.Extensions.DependencyInjection.xml"]}, "Autofac.Extras.DynamicProxy/7.1.0": {"sha512": "Da6Szv7A1LK/cTdeoyqS45zb/BC5vep8+86f6C1oh2UhZaYtiijlNfLWamp3lxe0uUQ33kFe1dDCjsvfwJWzLg==", "type": "package", "path": "autofac.extras.dynamicproxy/7.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "autofac.extras.dynamicproxy.7.1.0.nupkg.sha512", "autofac.extras.dynamicproxy.nuspec", "icon.png", "lib/net6.0/Autofac.Extras.DynamicProxy.dll", "lib/net6.0/Autofac.Extras.DynamicProxy.xml", "lib/netstandard2.0/Autofac.Extras.DynamicProxy.dll", "lib/netstandard2.0/Autofac.Extras.DynamicProxy.xml", "lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll", "lib/netstandard2.1/Autofac.Extras.DynamicProxy.xml"]}, "AutoMapper/13.0.1": {"sha512": "/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "type": "package", "path": "automapper/13.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "automapper.13.0.1.nupkg.sha512", "automapper.nuspec", "icon.png", "lib/net6.0/AutoMapper.dll", "lib/net6.0/AutoMapper.xml"]}, "Castle.Core/5.1.1": {"sha512": "rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "type": "package", "path": "castle.core/5.1.1", "files": [".nupkg.metadata", ".signature.p7s", "ASL - Apache Software Foundation License.txt", "CHANGELOG.md", "LICENSE", "castle-logo.png", "castle.core.5.1.1.nupkg.sha512", "castle.core.nuspec", "lib/net462/Castle.Core.dll", "lib/net462/Castle.Core.xml", "lib/net6.0/Castle.Core.dll", "lib/net6.0/Castle.Core.xml", "lib/netstandard2.0/Castle.Core.dll", "lib/netstandard2.0/Castle.Core.xml", "lib/netstandard2.1/Castle.Core.dll", "lib/netstandard2.1/Castle.Core.xml", "readme.txt"]}, "Castle.Core.AsyncInterceptor/2.1.0": {"sha512": "1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "type": "package", "path": "castle.core.asyncinterceptor/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "castle-logo.png", "castle.core.asyncinterceptor.2.1.0.nupkg.sha512", "castle.core.asyncinterceptor.nuspec", "lib/net45/Castle.Core.AsyncInterceptor.dll", "lib/net45/Castle.Core.AsyncInterceptor.xml", "lib/net5.0/Castle.Core.AsyncInterceptor.dll", "lib/net5.0/Castle.Core.AsyncInterceptor.xml", "lib/net6.0/Castle.Core.AsyncInterceptor.dll", "lib/net6.0/Castle.Core.AsyncInterceptor.xml", "lib/netstandard2.0/Castle.Core.AsyncInterceptor.dll", "lib/netstandard2.0/Castle.Core.AsyncInterceptor.xml"]}, "DeviceDetector.NET/6.1.4": {"sha512": "iZU0hCvtA6j9I5JMh41YYKYA2fkEKCP2g+bHyrBeyKAByUwl3uNlGqRG/jg3nIqsf8MfbJ9LlNeZAZ6XR20FYA==", "type": "package", "path": "devicedetector.net/6.1.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "devicedetector.net.6.1.4.nupkg.sha512", "devicedetector.net.nuspec", "lib/net462/DeviceDetector.NET.dll", "lib/net5.0/DeviceDetector.NET.dll", "lib/net6.0/DeviceDetector.NET.dll", "lib/net7.0/DeviceDetector.NET.dll", "lib/netstandard2.0/DeviceDetector.NET.dll", "logo.jpg"]}, "IdentityModel/6.2.0": {"sha512": "4AXZ6Tp+DNwrSSeBziiX/231i8ZpD77A9nEMyc68gLSCWG0kgWsIBeFquYcBebiIPkfB7GEXzCYuuLeR1QZJIQ==", "type": "package", "path": "identitymodel/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.jpg", "identitymodel.6.2.0.nupkg.sha512", "identitymodel.nuspec", "lib/net461/IdentityModel.dll", "lib/net461/IdentityModel.pdb", "lib/net461/IdentityModel.xml", "lib/net472/IdentityModel.dll", "lib/net472/IdentityModel.pdb", "lib/net472/IdentityModel.xml", "lib/net6.0/IdentityModel.dll", "lib/net6.0/IdentityModel.pdb", "lib/net6.0/IdentityModel.xml", "lib/netstandard2.0/IdentityModel.dll", "lib/netstandard2.0/IdentityModel.pdb", "lib/netstandard2.0/IdentityModel.xml"]}, "JetBrains.Annotations/2023.3.0": {"sha512": "PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "type": "package", "path": "jetbrains.annotations/2023.3.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "jetbrains.annotations.2023.3.0.nupkg.sha512", "jetbrains.annotations.nuspec", "lib/net20/JetBrains.Annotations.dll", "lib/net20/JetBrains.Annotations.xml", "lib/netstandard1.0/JetBrains.Annotations.deps.json", "lib/netstandard1.0/JetBrains.Annotations.dll", "lib/netstandard1.0/JetBrains.Annotations.xml", "lib/netstandard2.0/JetBrains.Annotations.deps.json", "lib/netstandard2.0/JetBrains.Annotations.dll", "lib/netstandard2.0/JetBrains.Annotations.xml", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.dll", "lib/portable40-net40+sl5+win8+wp8+wpa81/JetBrains.Annotations.xml"]}, "LiteDB/5.0.17": {"sha512": "cKPvkdlzIts3ZKu/BzoIc/Y71e4VFKlij4LyioPFATZMot+wB7EAm1FFbZSJez6coJmQUoIg/3yHE1MMU+zOdg==", "type": "package", "path": "litedb/5.0.17", "files": [".nupkg.metadata", ".signature.p7s", "AppVeyorSettings.json", "LICENSE", "icon_64x64.png", "lib/net45/LiteDB.dll", "lib/net45/LiteDB.xml", "lib/netstandard1.3/LiteDB.dll", "lib/netstandard1.3/LiteDB.xml", "lib/netstandard2.0/LiteDB.dll", "lib/netstandard2.0/LiteDB.xml", "litedb.5.0.17.nupkg.sha512", "litedb.nuspec"]}, "Microsoft.AspNetCore.Authentication.OpenIdConnect/8.0.4": {"sha512": "REVWecEYp7b9xuZzRgq09QjeQTlpA478AEZYWsjc7aa8zu+/TrAGRQbZW2YT3VpsdNl/Fv592sKt0gAnt+0hEw==", "type": "package", "path": "microsoft.aspnetcore.authentication.openidconnect/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.dll", "lib/net8.0/Microsoft.AspNetCore.Authentication.OpenIdConnect.xml", "microsoft.aspnetcore.authentication.openidconnect.8.0.4.nupkg.sha512", "microsoft.aspnetcore.authentication.openidconnect.nuspec"]}, "Microsoft.AspNetCore.Authorization/8.0.4": {"sha512": "dzYUHvqyavBiYVd5BZNAgV5rT+DC4HyIWPnbeYgxQNBYOFomvvwzPfidQf1Ld/HhkXhEj3tlzqNb4gy2P2ukUg==", "type": "package", "path": "microsoft.aspnetcore.authorization/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Authorization.dll", "lib/net462/Microsoft.AspNetCore.Authorization.xml", "lib/net8.0/Microsoft.AspNetCore.Authorization.dll", "lib/net8.0/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Authorization.xml", "microsoft.aspnetcore.authorization.8.0.4.nupkg.sha512", "microsoft.aspnetcore.authorization.nuspec"]}, "Microsoft.AspNetCore.Metadata/8.0.4": {"sha512": "TG3JhI7dkDCIoxZ3TL/5yptLYLg56w1f4jE8GSYnZXSso1xY5JKKCxObK+xKQJU5kZir30+QUGX1WpbC1kDcow==", "type": "package", "path": "microsoft.aspnetcore.metadata/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.AspNetCore.Metadata.dll", "lib/net462/Microsoft.AspNetCore.Metadata.xml", "lib/net8.0/Microsoft.AspNetCore.Metadata.dll", "lib/net8.0/Microsoft.AspNetCore.Metadata.xml", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.dll", "lib/netstandard2.0/Microsoft.AspNetCore.Metadata.xml", "microsoft.aspnetcore.metadata.8.0.4.nupkg.sha512", "microsoft.aspnetcore.metadata.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.Extensions/6.0.0": {"sha512": "M0h+ChPgydX2xY17agiphnAVa/Qh05RAP8eeuqGGhQKT10claRBlLNO6d2/oSV8zy0RLHzwLnNZm5xuC/gckGA==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.extensions/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Mvc.Razor.Extensions.dll", "microsoft.aspnetcore.mvc.razor.extensions.6.0.0.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.extensions.nuspec"]}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation/8.0.4": {"sha512": "876m+YvEo+rjbZNQv64RhOBtWVD08bOwT/g96G0cBm+810WiSR3f5C79XG+W59PuvF6JLGFz+IXYLHCjLCvOrQ==", "type": "package", "path": "microsoft.aspnetcore.mvc.razor.runtimecompilation/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "build/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "buildTransitive/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.targets", "lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.dll", "lib/net8.0/Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation.xml", "microsoft.aspnetcore.mvc.razor.runtimecompilation.8.0.4.nupkg.sha512", "microsoft.aspnetcore.mvc.razor.runtimecompilation.nuspec"]}, "Microsoft.AspNetCore.Razor.Language/6.0.0": {"sha512": "yCtBr1GSGzJrrp1NJUb4ltwFYMKHw/tJLnIDvg9g/FnkGIEzmE19tbCQqXARIJv5kdtBgsoVIdGLL+zmjxvM/A==", "type": "package", "path": "microsoft.aspnetcore.razor.language/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll", "microsoft.aspnetcore.razor.language.6.0.0.nupkg.sha512", "microsoft.aspnetcore.razor.language.nuspec"]}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"sha512": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.CodeAnalysis.Analyzers/3.3.2": {"sha512": "7xt6zTlIEizUgEsYAIgm37EbdkiMmr6fP6J9pDoKEpiGM4pi32BCPGr/IczmSJI9Zzp0a6HOzpr9OvpMP+2veA==", "type": "package", "path": "microsoft.codeanalysis.analyzers/3.3.2", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "EULA.rtf", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/cs/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "analyzers/dotnet/vb/cs/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/de/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/es/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/fr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/it/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ja/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ko/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pl/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/pt-BR/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/ru/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/tr/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-<PERSON>/Microsoft.CodeAnalysis.Analyzers.resources.dll", "analyzers/dotnet/vb/zh-Hant/Microsoft.CodeAnalysis.Analyzers.resources.dll", "build/Microsoft.CodeAnalysis.Analyzers.props", "build/Microsoft.CodeAnalysis.Analyzers.targets", "build/config/AnalysisLevel_2_9_8_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_2_9_8_Default.editorconfig", "build/config/AnalysisLevel_3_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_3_Default.editorconfig", "build/config/AnalysisLevel_3_AllDisabledByDefault.editorconfig", "build/config/AnalysisLevel_3_AllEnabledByDefault.editorconfig", "build/config/AnalysisLevel_3_Default.editorconfig", "documentation/Analyzer Configuration.md", "documentation/Microsoft.CodeAnalysis.Analyzers.md", "documentation/Microsoft.CodeAnalysis.Analyzers.sarif", "editorconfig/AllRulesDefault/.editorconfig", "editorconfig/AllRulesDisabled/.editorconfig", "editorconfig/AllRulesEnabled/.editorconfig", "editorconfig/CorrectnessRulesDefault/.editorconfig", "editorconfig/CorrectnessRulesEnabled/.editorconfig", "editorconfig/DataflowRulesDefault/.editorconfig", "editorconfig/DataflowRulesEnabled/.editorconfig", "editorconfig/LibraryRulesDefault/.editorconfig", "editorconfig/LibraryRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCompatibilityRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisCorrectnessRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDesignRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisDocumentationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisLocalizationRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisPerformanceRulesEnabled/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesDefault/.editorconfig", "editorconfig/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled/.editorconfig", "editorconfig/PortedFromFxCopRulesDefault/.editorconfig", "editorconfig/PortedFromFxCopRulesEnabled/.editorconfig", "microsoft.codeanalysis.analyzers.3.3.2.nupkg.sha512", "microsoft.codeanalysis.analyzers.nuspec", "rulesets/AllRulesDefault.ruleset", "rulesets/AllRulesDisabled.ruleset", "rulesets/AllRulesEnabled.ruleset", "rulesets/CorrectnessRulesDefault.ruleset", "rulesets/CorrectnessRulesEnabled.ruleset", "rulesets/DataflowRulesDefault.ruleset", "rulesets/DataflowRulesEnabled.ruleset", "rulesets/LibraryRulesDefault.ruleset", "rulesets/LibraryRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCompatibilityRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisCorrectnessRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDesignRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisDocumentationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisLocalizationRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisPerformanceRulesEnabled.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesDefault.ruleset", "rulesets/MicrosoftCodeAnalysisReleaseTrackingRulesEnabled.ruleset", "rulesets/PortedFromFxCopRulesDefault.ruleset", "rulesets/PortedFromFxCopRulesEnabled.ruleset", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/4.0.0": {"sha512": "d02ybMhUJl1r/dI6SkJPHrTiTzXBYCZeJdOLMckV+jyoMU/GGkjqFX/sRbv1K0QmlpwwKuLTiYVQvfYC+8ox2g==", "type": "package", "path": "microsoft.codeanalysis.common/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "microsoft.codeanalysis.common.4.0.0.nupkg.sha512", "microsoft.codeanalysis.common.nuspec"]}, "Microsoft.CodeAnalysis.CSharp/4.0.0": {"sha512": "2UVTGtyQGgTCazvnT6t82f+7AV2L+kqJdyb61rT9GQed4yK+tVh5IkaKcsm70VqyZQhBbDqsfZFNHnY65xhrRw==", "type": "package", "path": "microsoft.codeanalysis.csharp/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "ThirdPartyNotices.rtf", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.xml", "lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.pdb", "lib/netstandard2.0/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard2.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.CodeAnalysis.CSharp.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "microsoft.codeanalysis.csharp.4.0.0.nupkg.sha512", "microsoft.codeanalysis.csharp.nuspec"]}, "Microsoft.CodeAnalysis.Razor/6.0.0": {"sha512": "uqdzuQXxD7XrJCbIbbwpI/LOv0PBJ9VIR0gdvANTHOfK5pjTaCir+XcwvYvBZ5BIzd0KGzyiamzlEWw1cK1q0w==", "type": "package", "path": "microsoft.codeanalysis.razor/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll", "microsoft.codeanalysis.razor.6.0.0.nupkg.sha512", "microsoft.codeanalysis.razor.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"sha512": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "type": "package", "path": "microsoft.extensions.caching.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"sha512": "7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "type": "package", "path": "microsoft.extensions.caching.memory/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"sha512": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "type": "package", "path": "microsoft.extensions.configuration.binder/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"sha512": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "type": "package", "path": "microsoft.extensions.configuration.commandline/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net6.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net7.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"sha512": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net6.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net7.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"sha512": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net6.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net6.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net7.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"sha512": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/8.0.0": {"sha512": "NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "type": "package", "path": "microsoft.extensions.dependencymodel/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"sha512": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"sha512": "ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "type": "package", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Composite.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Composite.targets", "lib/net462/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net462/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Composite.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Composite.xml", "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.composite.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Embedded/8.0.4": {"sha512": "r3wpZ7RSjDqtMQmsIICjOrOylCnOlJJ0nWcnsuLb+iyLslBEe2+wHAI7xCmEMDu8ZP1K5qSryXH8Kt4o6Lyn9g==", "type": "package", "path": "microsoft.extensions.fileproviders.embedded/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.props", "build/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.targets", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.props", "buildMultiTargeting/Microsoft.Extensions.FileProviders.Embedded.targets", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net462/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.xml", "microsoft.extensions.fileproviders.embedded.8.0.4.nupkg.sha512", "microsoft.extensions.fileproviders.embedded.nuspec", "tasks/netstandard2.0/Microsoft.Extensions.FileProviders.Embedded.Manifest.Task.dll"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"sha512": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Localization/8.0.0": {"sha512": "I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "type": "package", "path": "microsoft.extensions.localization/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.dll", "lib/net462/Microsoft.Extensions.Localization.xml", "lib/net8.0/Microsoft.Extensions.Localization.dll", "lib/net8.0/Microsoft.Extensions.Localization.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.xml", "microsoft.extensions.localization.8.0.0.nupkg.sha512", "microsoft.extensions.localization.nuspec"]}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"sha512": "LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "type": "package", "path": "microsoft.extensions.localization.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net462/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net462/Microsoft.Extensions.Localization.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Localization.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Localization.Abstractions.xml", "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.localization.abstractions.nuspec"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"sha512": "RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.2": {"sha512": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "type": "package", "path": "microsoft.extensions.options/8.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.2.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"sha512": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net6.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net7.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"sha512": "33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"sha512": "cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.1.2": {"sha512": "YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "type": "package", "path": "microsoft.identitymodel.logging/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.1.2.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.1.2": {"sha512": "SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "type": "package", "path": "microsoft.identitymodel.protocols/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"sha512": "6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.1.2": {"sha512": "oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "type": "package", "path": "microsoft.identitymodel.tokens/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/3.1.0": {"sha512": "z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "type": "package", "path": "microsoft.netcore.platforms/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.OpenApi/1.2.3": {"sha512": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "type": "package", "path": "microsoft.openapi/1.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Microsoft.OpenApi.dll", "lib/net46/Microsoft.OpenApi.pdb", "lib/net46/Microsoft.OpenApi.xml", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.2.3.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "Nito.AsyncEx.Context/5.1.2": {"sha512": "rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "type": "package", "path": "nito.asyncex.context/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Context.dll", "lib/net461/Nito.AsyncEx.Context.xml", "lib/netstandard1.3/Nito.AsyncEx.Context.dll", "lib/netstandard1.3/Nito.AsyncEx.Context.xml", "lib/netstandard2.0/Nito.AsyncEx.Context.dll", "lib/netstandard2.0/Nito.AsyncEx.Context.xml", "nito.asyncex.context.5.1.2.nupkg.sha512", "nito.asyncex.context.nuspec"]}, "Nito.AsyncEx.Tasks/5.1.2": {"sha512": "jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "type": "package", "path": "nito.asyncex.tasks/5.1.2", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.AsyncEx.Tasks.dll", "lib/net461/Nito.AsyncEx.Tasks.xml", "lib/netstandard1.3/Nito.AsyncEx.Tasks.dll", "lib/netstandard1.3/Nito.AsyncEx.Tasks.xml", "lib/netstandard2.0/Nito.AsyncEx.Tasks.dll", "lib/netstandard2.0/Nito.AsyncEx.Tasks.xml", "nito.asyncex.tasks.5.1.2.nupkg.sha512", "nito.asyncex.tasks.nuspec"]}, "Nito.Disposables/2.2.1": {"sha512": "6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "type": "package", "path": "nito.disposables/2.2.1", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net461/Nito.Disposables.dll", "lib/net461/Nito.Disposables.xml", "lib/netstandard1.0/Nito.Disposables.dll", "lib/netstandard1.0/Nito.Disposables.xml", "lib/netstandard2.0/Nito.Disposables.dll", "lib/netstandard2.0/Nito.Disposables.xml", "lib/netstandard2.1/Nito.Disposables.dll", "lib/netstandard2.1/Nito.Disposables.xml", "nito.disposables.2.2.1.nupkg.sha512", "nito.disposables.nuspec"]}, "NUglify/1.21.0": {"sha512": "9J44o89PRdcS4GOyj0obkLfjCAuJItI4FrNmwALkjRKlzvHVlTB2ALbC9aigIoCMqzy0Xlc0mIVD/jO9WVDHiA==", "type": "package", "path": "nuglify/1.21.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/NUglify.dll", "lib/net35/NUglify.xml", "lib/net40/NUglify.dll", "lib/net40/NUglify.xml", "lib/net5.0/NUglify.dll", "lib/net5.0/NUglify.xml", "lib/netstandard1.3/NUglify.dll", "lib/netstandard1.3/NUglify.xml", "lib/netstandard2.0/NUglify.dll", "lib/netstandard2.0/NUglify.xml", "nuglify.1.21.0.nupkg.sha512", "nuglify.nuspec", "nuglify.png"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "SqlSugar/*********": {"sha512": "5BFK7HOBYu/DC0NawHHH+nGGF4Kft3P510nvQ84txm215M9D/ru/9AGlkEw2oOYmdJ+ZZstA5GDC33kPJiMniQ==", "type": "package", "path": "sqlsugar/*********", "files": [".nupkg.metadata", ".signature.p7s", "lib/SqlSugar.dll", "sqlsugar.*********.nupkg.sha512", "sqlsugar.nuspec"]}, "Swashbuckle.AspNetCore/6.5.0": {"sha512": "FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "type": "package", "path": "swashbuckle.aspnetcore/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"sha512": "XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net7.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"sha512": "Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"sha512": "OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/6.0.0": {"sha512": "lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "type": "package", "path": "system.diagnostics.eventlog/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.EventLog.dll", "lib/net461/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.dll", "runtimes/win/lib/netcoreapp3.1/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.6.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"sha512": "Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Dynamic.Core/1.3.5": {"sha512": "G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "type": "package", "path": "system.linq.dynamic.core/1.3.5", "files": [".nupkg.metadata", ".signature.p7s", "PackageReadme.md", "lib/net35/System.Linq.Dynamic.Core.dll", "lib/net35/System.Linq.Dynamic.Core.pdb", "lib/net35/System.Linq.Dynamic.Core.xml", "lib/net40/System.Linq.Dynamic.Core.dll", "lib/net40/System.Linq.Dynamic.Core.pdb", "lib/net40/System.Linq.Dynamic.Core.xml", "lib/net45/System.Linq.Dynamic.Core.dll", "lib/net45/System.Linq.Dynamic.Core.pdb", "lib/net45/System.Linq.Dynamic.Core.xml", "lib/net452/System.Linq.Dynamic.Core.dll", "lib/net452/System.Linq.Dynamic.Core.pdb", "lib/net452/System.Linq.Dynamic.Core.xml", "lib/net46/System.Linq.Dynamic.Core.dll", "lib/net46/System.Linq.Dynamic.Core.pdb", "lib/net46/System.Linq.Dynamic.Core.xml", "lib/net5.0/System.Linq.Dynamic.Core.dll", "lib/net5.0/System.Linq.Dynamic.Core.pdb", "lib/net5.0/System.Linq.Dynamic.Core.xml", "lib/net6.0/System.Linq.Dynamic.Core.dll", "lib/net6.0/System.Linq.Dynamic.Core.pdb", "lib/net6.0/System.Linq.Dynamic.Core.xml", "lib/net7.0/System.Linq.Dynamic.Core.dll", "lib/net7.0/System.Linq.Dynamic.Core.pdb", "lib/net7.0/System.Linq.Dynamic.Core.xml", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp2.1/System.Linq.Dynamic.Core.xml", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.dll", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.pdb", "lib/netcoreapp3.1/System.Linq.Dynamic.Core.xml", "lib/netstandard1.3/System.Linq.Dynamic.Core.dll", "lib/netstandard1.3/System.Linq.Dynamic.Core.pdb", "lib/netstandard1.3/System.Linq.Dynamic.Core.xml", "lib/netstandard2.0/System.Linq.Dynamic.Core.dll", "lib/netstandard2.0/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.0/System.Linq.Dynamic.Core.xml", "lib/netstandard2.1/System.Linq.Dynamic.Core.dll", "lib/netstandard2.1/System.Linq.Dynamic.Core.pdb", "lib/netstandard2.1/System.Linq.Dynamic.Core.xml", "lib/uap10.0.10240/System.Linq.Dynamic.Core.dll", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pdb", "lib/uap10.0.10240/System.Linq.Dynamic.Core.pri", "lib/uap10.0.10240/System.Linq.Dynamic.Core.xml", "logo.png", "system.linq.dynamic.core.1.3.5.nupkg.sha512", "system.linq.dynamic.core.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Linq.Queryable/4.3.0": {"sha512": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "type": "package", "path": "system.linq.queryable/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.queryable.4.3.0.nupkg.sha512", "system.linq.queryable.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Emit/4.3.0": {"sha512": "228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "type": "package", "path": "system.reflection.emit/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._", "system.reflection.emit.4.3.0.nupkg.sha512", "system.reflection.emit.nuspec"]}, "System.Reflection.Emit.ILGeneration/4.3.0": {"sha512": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "type": "package", "path": "system.reflection.emit.ilgeneration/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "system.reflection.emit.ilgeneration.nuspec"]}, "System.Reflection.Emit.Lightweight/4.3.0": {"sha512": "oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "type": "package", "path": "system.reflection.emit.lightweight/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/_._", "system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "system.reflection.emit.lightweight.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Metadata/5.0.0": {"sha512": "5NecZgXktdGg34rh1OenY1rFNDCI8xSjFr+Z4OU4cU06AQHUdRnIIEeWENu3Wl4YowbzkymAIMvi3WyK9U53pQ==", "type": "package", "path": "system.reflection.metadata/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Reflection.Metadata.dll", "lib/net461/System.Reflection.Metadata.xml", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/netstandard2.0/System.Reflection.Metadata.dll", "lib/netstandard2.0/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml", "system.reflection.metadata.5.0.0.nupkg.sha512", "system.reflection.metadata.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Reflection.TypeExtensions/4.3.0": {"sha512": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "type": "package", "path": "system.reflection.typeextensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll", "system.reflection.typeextensions.4.3.0.nupkg.sha512", "system.reflection.typeextensions.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"sha512": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "type": "package", "path": "system.runtime.compilerservices.unsafe/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Runtime.CompilerServices.Unsafe.dll", "lib/net45/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/net461/System.Runtime.CompilerServices.Unsafe.dll", "ref/net461/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.1/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Loader/4.3.0": {"sha512": "DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "type": "package", "path": "system.runtime.loader/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml", "system.runtime.loader.4.3.0.nupkg.sha512", "system.runtime.loader.nuspec"]}, "System.Security.AccessControl/4.7.0": {"sha512": "JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "type": "package", "path": "system.security.accesscontrol/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.4.7.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.CodePages/4.5.1": {"sha512": "4J2JQXbftjPMppIHJ7IC+VXQ9XfEagN92vZZNoG12i+zReYlim5dMoXFC1Zzg7tsnKDM7JPo5bYfFK4Jheq44w==", "type": "package", "path": "system.text.encoding.codepages/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/net461/System.Text.Encoding.CodePages.dll", "lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net461/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "system.text.encoding.codepages.4.5.1.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.4": {"sha512": "bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "type": "package", "path": "system.text.json/8.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.4.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "TimeZoneConverter/6.1.0": {"sha512": "UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "type": "package", "path": "timezoneconverter/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/TimeZoneConverter.dll", "lib/net462/TimeZoneConverter.xml", "lib/net6.0/TimeZoneConverter.dll", "lib/net6.0/TimeZoneConverter.xml", "lib/netstandard2.0/TimeZoneConverter.dll", "lib/netstandard2.0/TimeZoneConverter.xml", "timezoneconverter.6.1.0.nupkg.sha512", "timezoneconverter.nuspec"]}, "Volo.Abp.ApiVersioning.Abstractions/8.3.4": {"sha512": "jsJElnvLghxS/E1DGRrJUjeoXETfGcg68goIlo8us98cB19jCj14DTGsOOB7Ty84afZS5Ym6u3TcWl5Z8UI6GA==", "type": "package", "path": "volo.abp.apiversioning.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ApiVersioning.Abstractions.abppkg", "content/Volo.Abp.ApiVersioning.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/net8.0/Volo.Abp.ApiVersioning.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.ApiVersioning.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.ApiVersioning.Abstractions.xml", "volo.abp.apiversioning.abstractions.8.3.4.nupkg.sha512", "volo.abp.apiversioning.abstractions.nuspec"]}, "Volo.Abp.AspNetCore/8.3.4": {"sha512": "7cjzVZ6mxVlAMhYK9vqzFjDFP6NETxL9BodF6R1J0Pp/r6t+CueMl1B4Se2XkNs2stXThkcjWLj6Zr38QDxkUQ==", "type": "package", "path": "volo.abp.aspnetcore/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.abppkg", "content/Volo.Abp.AspNetCore.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.dll", "lib/net8.0/Volo.Abp.AspNetCore.pdb", "lib/net8.0/Volo.Abp.AspNetCore.xml", "volo.abp.aspnetcore.8.3.4.nupkg.sha512", "volo.abp.aspnetcore.nuspec"]}, "Volo.Abp.AspNetCore.Abstractions/8.3.4": {"sha512": "neXc2jiP1aZwS0f4kooQbTkWeLX3q0MPRwuX+F6HorF+QkP8kyymLUJjCTyee8z97QNzPxlgTcNLIm4yotr1Zg==", "type": "package", "path": "volo.abp.aspnetcore.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.AspNetCore.Abstractions.dll", "lib/net8.0/Volo.Abp.AspNetCore.Abstractions.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.AspNetCore.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.AspNetCore.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.AspNetCore.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.AspNetCore.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.AspNetCore.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.AspNetCore.Abstractions.xml", "volo.abp.aspnetcore.abstractions.8.3.4.nupkg.sha512", "volo.abp.aspnetcore.abstractions.nuspec"]}, "Volo.Abp.AspNetCore.Mvc/8.3.4": {"sha512": "Q8hFwv7ZrmB+gjOr3HbfIGOcZ7WlMkHNMIjY2OonvcgPuDeYdEB41Ok9ZRhOHVkZRMjc8XM44AktXtJq6ouj7A==", "type": "package", "path": "volo.abp.aspnetcore.mvc/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.abppkg", "content/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json", "contentFiles/any/net8.0/Volo.Abp.AspNetCore.Mvc.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.xml", "volo.abp.aspnetcore.mvc.8.3.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.nuspec"]}, "Volo.Abp.AspNetCore.Mvc.Contracts/8.3.4": {"sha512": "lfl8Vhc45c/XWRq8oghyqhnJbdUQDU/4ZQL0bwkqHRQg2tUZUQrsSrnitXAnaS62HBBLgLTeujcSKLfPPcw3cg==", "type": "package", "path": "volo.abp.aspnetcore.mvc.contracts/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AspNetCore.Mvc.Contracts.abppkg", "content/Volo.Abp.AspNetCore.Mvc.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/net8.0/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.dll", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.AspNetCore.Mvc.Contracts.xml", "volo.abp.aspnetcore.mvc.contracts.8.3.4.nupkg.sha512", "volo.abp.aspnetcore.mvc.contracts.nuspec"]}, "Volo.Abp.Auditing/8.3.4": {"sha512": "QDQrhj5OaYRKGAOt305YSEl5VPQWBl+/J7EqJt/NeD2qPPSIVIOCEGN+zpXDwOxOF9/8cZeyzIV8SVE1Ln+6Yw==", "type": "package", "path": "volo.abp.auditing/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Auditing.abppkg", "content/Volo.Abp.Auditing.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Auditing.dll", "lib/net8.0/Volo.Abp.Auditing.pdb", "lib/net8.0/Volo.Abp.Auditing.xml", "lib/netstandard2.0/Volo.Abp.Auditing.dll", "lib/netstandard2.0/Volo.Abp.Auditing.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.xml", "lib/netstandard2.1/Volo.Abp.Auditing.dll", "lib/netstandard2.1/Volo.Abp.Auditing.pdb", "lib/netstandard2.1/Volo.Abp.Auditing.xml", "volo.abp.auditing.8.3.4.nupkg.sha512", "volo.abp.auditing.nuspec"]}, "Volo.Abp.Auditing.Contracts/8.3.4": {"sha512": "uw7U+gqLTcQJEhrk2pPsleTcQTV/ej7inBAfrAnYs4m4adAZzzCXtJKI4eqmjSWWwcZrQ1hwLOwKWFSuQr39Hw==", "type": "package", "path": "volo.abp.auditing.contracts/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Auditing.Contracts.abppkg", "content/Volo.Abp.Auditing.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Auditing.Contracts.dll", "lib/net8.0/Volo.Abp.Auditing.Contracts.pdb", "lib/net8.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Auditing.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Auditing.Contracts.xml", "volo.abp.auditing.contracts.8.3.4.nupkg.sha512", "volo.abp.auditing.contracts.nuspec"]}, "Volo.Abp.Authorization/8.3.4": {"sha512": "LwFhq4PmNDsekYrUm6LCYXJzNXYlxlXwfBnKMqwstiuUesr6IJEY5oOHvhRvZKPxaRjWTrDObtqFfJpnd/jHqg==", "type": "package", "path": "volo.abp.authorization/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.abppkg", "content/Volo.Abp.Authorization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.dll", "lib/net8.0/Volo.Abp.Authorization.pdb", "lib/net8.0/Volo.Abp.Authorization.xml", "lib/netstandard2.0/Volo.Abp.Authorization.dll", "lib/netstandard2.0/Volo.Abp.Authorization.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.xml", "lib/netstandard2.1/Volo.Abp.Authorization.dll", "lib/netstandard2.1/Volo.Abp.Authorization.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.xml", "volo.abp.authorization.8.3.4.nupkg.sha512", "volo.abp.authorization.nuspec"]}, "Volo.Abp.Authorization.Abstractions/8.3.4": {"sha512": "vG7ePbrh+Tyx9G/KgTnp4bmcKszDSsEZExbSl5YRO2pW8OgTDCZ4USjnH8VHG0LE5I+sIlL8IWCvkhfExTX4Og==", "type": "package", "path": "volo.abp.authorization.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Authorization.Abstractions.abppkg", "content/Volo.Abp.Authorization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Authorization.Abstractions.dll", "lib/net8.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Authorization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Authorization.Abstractions.xml", "volo.abp.authorization.abstractions.8.3.4.nupkg.sha512", "volo.abp.authorization.abstractions.nuspec"]}, "Volo.Abp.Autofac/8.3.4": {"sha512": "YmAH0h6e602Lt6zaVR4GooLIS2FuOZWr9BhNqjnZfEHrO6s4v+YStrfReTWh1YgeZ8p+l+9Wqa2+6mM962NFOQ==", "type": "package", "path": "volo.abp.autofac/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Autofac.abppkg", "lib/net8.0/Volo.Abp.Autofac.dll", "lib/net8.0/Volo.Abp.Autofac.pdb", "lib/net8.0/Volo.Abp.Autofac.xml", "lib/netstandard2.0/Volo.Abp.Autofac.dll", "lib/netstandard2.0/Volo.Abp.Autofac.pdb", "lib/netstandard2.0/Volo.Abp.Autofac.xml", "lib/netstandard2.1/Volo.Abp.Autofac.dll", "lib/netstandard2.1/Volo.Abp.Autofac.pdb", "lib/netstandard2.1/Volo.Abp.Autofac.xml", "volo.abp.autofac.8.3.4.nupkg.sha512", "volo.abp.autofac.nuspec"]}, "Volo.Abp.AutoMapper/8.3.4": {"sha512": "qbVuLzuFIcrTUliiJ5ZPMG6N6g3QSLB7E+cKQ4g4+PHZYE6bqAufdjL8mizP/dkuEGPOi6mEVz5EHe5aCUnhJA==", "type": "package", "path": "volo.abp.automapper/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.AutoMapper.abppkg", "content/Volo.Abp.AutoMapper.abppkg.analyze.json", "lib/net8.0/Volo.Abp.AutoMapper.dll", "lib/net8.0/Volo.Abp.AutoMapper.pdb", "lib/net8.0/Volo.Abp.AutoMapper.xml", "volo.abp.automapper.8.3.4.nupkg.sha512", "volo.abp.automapper.nuspec"]}, "Volo.Abp.BackgroundWorkers/8.3.4": {"sha512": "ZP8G+ebwqQNBePYYK3goEheCG20SJvXLyS9+bUTTyd2u3ZqnA7twvdL9+oRdVL6rZVWGabZ73T37kiOckyxYNQ==", "type": "package", "path": "volo.abp.backgroundworkers/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.BackgroundWorkers.abppkg", "content/Volo.Abp.BackgroundWorkers.abppkg.analyze.json", "lib/net8.0/Volo.Abp.BackgroundWorkers.dll", "lib/net8.0/Volo.Abp.BackgroundWorkers.pdb", "lib/net8.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.0/Volo.Abp.BackgroundWorkers.xml", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.dll", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.pdb", "lib/netstandard2.1/Volo.Abp.BackgroundWorkers.xml", "volo.abp.backgroundworkers.8.3.4.nupkg.sha512", "volo.abp.backgroundworkers.nuspec"]}, "Volo.Abp.Caching/8.3.4": {"sha512": "nV0ZC96F7T6sJdw+QikOucXRSI8A2daPdNMMAhoh2hVCU9YrMdSygT4wei8+BWqlhXfAltUotJvrd86FB1N18A==", "type": "package", "path": "volo.abp.caching/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Caching.abppkg", "content/Volo.Abp.Caching.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Caching.dll", "lib/net8.0/Volo.Abp.Caching.pdb", "lib/net8.0/Volo.Abp.Caching.xml", "lib/netstandard2.0/Volo.Abp.Caching.dll", "lib/netstandard2.0/Volo.Abp.Caching.pdb", "lib/netstandard2.0/Volo.Abp.Caching.xml", "lib/netstandard2.1/Volo.Abp.Caching.dll", "lib/netstandard2.1/Volo.Abp.Caching.pdb", "lib/netstandard2.1/Volo.Abp.Caching.xml", "volo.abp.caching.8.3.4.nupkg.sha512", "volo.abp.caching.nuspec"]}, "Volo.Abp.Castle.Core/8.3.4": {"sha512": "0F96Bmt7F0YVy11YmODvEiqzVzvBzUMRD1zDyWPuYOyO2nEIlkA4Un6F3WKBAEJfvrQZoCVWXLHD1aPhgbX1Pg==", "type": "package", "path": "volo.abp.castle.core/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Castle.Core.abppkg", "content/Volo.Abp.Castle.Core.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Castle.Core.dll", "lib/net8.0/Volo.Abp.Castle.Core.pdb", "lib/net8.0/Volo.Abp.Castle.Core.xml", "lib/netstandard2.0/Volo.Abp.Castle.Core.dll", "lib/netstandard2.0/Volo.Abp.Castle.Core.pdb", "lib/netstandard2.0/Volo.Abp.Castle.Core.xml", "lib/netstandard2.1/Volo.Abp.Castle.Core.dll", "lib/netstandard2.1/Volo.Abp.Castle.Core.pdb", "lib/netstandard2.1/Volo.Abp.Castle.Core.xml", "volo.abp.castle.core.8.3.4.nupkg.sha512", "volo.abp.castle.core.nuspec"]}, "Volo.Abp.Core/8.3.4": {"sha512": "PhjE9M4df1x0wczDftMH5vgTFOSVixBOPNoRX5dsJFagAIn2Z/xxf4XI6gpvnS77tUNvWoVQZHyiy9cnPCsT2A==", "type": "package", "path": "volo.abp.core/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Core.abppkg", "content/Volo.Abp.Core.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Core.dll", "lib/net8.0/Volo.Abp.Core.pdb", "lib/net8.0/Volo.Abp.Core.xml", "lib/netstandard2.0/Volo.Abp.Core.dll", "lib/netstandard2.0/Volo.Abp.Core.pdb", "lib/netstandard2.0/Volo.Abp.Core.xml", "lib/netstandard2.1/Volo.Abp.Core.dll", "lib/netstandard2.1/Volo.Abp.Core.pdb", "lib/netstandard2.1/Volo.Abp.Core.xml", "volo.abp.core.8.3.4.nupkg.sha512", "volo.abp.core.nuspec"]}, "Volo.Abp.Data/8.3.4": {"sha512": "pKyUySy4CQvOhkzsHt66xDIxvVuARisQV+pA3bAwF3qkGXusV56KoqZ7CUNfD1rMUVf0yvCjsKqRGZsIkIsD7w==", "type": "package", "path": "volo.abp.data/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Data.abppkg", "content/Volo.Abp.Data.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Data.dll", "lib/net8.0/Volo.Abp.Data.pdb", "lib/net8.0/Volo.Abp.Data.xml", "lib/netstandard2.0/Volo.Abp.Data.dll", "lib/netstandard2.0/Volo.Abp.Data.pdb", "lib/netstandard2.0/Volo.Abp.Data.xml", "lib/netstandard2.1/Volo.Abp.Data.dll", "lib/netstandard2.1/Volo.Abp.Data.pdb", "lib/netstandard2.1/Volo.Abp.Data.xml", "volo.abp.data.8.3.4.nupkg.sha512", "volo.abp.data.nuspec"]}, "Volo.Abp.Ddd.Application/8.3.4": {"sha512": "eMsgNDgG1DHKGQ0BDT7QVFuIgOuJ1/cKakEV5b344Fz4o9BE2s9vDBjyHFYkixKprlJ1ubXp58+/aGIY7C6Brg==", "type": "package", "path": "volo.abp.ddd.application/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Application.abppkg", "content/Volo.Abp.Ddd.Application.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Application.dll", "lib/net8.0/Volo.Abp.Ddd.Application.pdb", "lib/net8.0/Volo.Abp.Ddd.Application.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Application.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Application.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Application.xml", "volo.abp.ddd.application.8.3.4.nupkg.sha512", "volo.abp.ddd.application.nuspec"]}, "Volo.Abp.Ddd.Application.Contracts/8.3.4": {"sha512": "ybs3SqmyJCjdJ9yWzUDur5i0KpnGtn8McYaUFwoTq6VhMm7yFDznGKKw/Xvm2K/pUjfgjgBfdQ2VdLcmr2eGKg==", "type": "package", "path": "volo.abp.ddd.application.contracts/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Application.Contracts.abppkg", "content/Volo.Abp.Ddd.Application.Contracts.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/net8.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Application.Contracts.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Application.Contracts.xml", "volo.abp.ddd.application.contracts.8.3.4.nupkg.sha512", "volo.abp.ddd.application.contracts.nuspec"]}, "Volo.Abp.Ddd.Domain/8.3.4": {"sha512": "NIMuAw7NOLUXA6GSYllO+cMvs/Ggu9t0fsPYR+mBWy/HvDRm1IK3xQFOoWaQVwtdBKaH6nSJ1NTnCSfrSats+A==", "type": "package", "path": "volo.abp.ddd.domain/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Ddd.Domain.abppkg", "content/Volo.Abp.Ddd.Domain.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Ddd.Domain.dll", "lib/net8.0/Volo.Abp.Ddd.Domain.pdb", "lib/net8.0/Volo.Abp.Ddd.Domain.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.xml", "volo.abp.ddd.domain.8.3.4.nupkg.sha512", "volo.abp.ddd.domain.nuspec"]}, "Volo.Abp.Ddd.Domain.Shared/8.3.4": {"sha512": "GwN4ohVjt2jmol6iBBbBdBIwRHMxKPwaFUE28rbtwVeCgV1Fj1iy8nGhYUxE6qgQtpLsljUvXYhgbr+vrheO0Q==", "type": "package", "path": "volo.abp.ddd.domain.shared/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/net8.0/Volo.Abp.Ddd.Domain.Shared.xml", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.dll", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/netstandard2.0/Volo.Abp.Ddd.Domain.Shared.xml", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.dll", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.pdb", "lib/netstandard2.1/Volo.Abp.Ddd.Domain.Shared.xml", "volo.abp.ddd.domain.shared.8.3.4.nupkg.sha512", "volo.abp.ddd.domain.shared.nuspec"]}, "Volo.Abp.DistributedLocking.Abstractions/8.3.4": {"sha512": "RJWB+hVtLDdo6LE+BSdud92G7hVSfoOGcDMEDzqveXrueDtCfeCZHgse1AW5wJT8trU/K+DeRkAFJpie7a7O6g==", "type": "package", "path": "volo.abp.distributedlocking.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg", "content/Volo.Abp.DistributedLocking.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.DistributedLocking.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.DistributedLocking.Abstractions.xml", "volo.abp.distributedlocking.abstractions.8.3.4.nupkg.sha512", "volo.abp.distributedlocking.abstractions.nuspec"]}, "Volo.Abp.EventBus/8.3.4": {"sha512": "j6mnITzG/8X5Ci9caUqOSVH+98I+5CySJ1qwViuARQ3+PhPgsQfFaoZ66SVHmNjbbGkltz9F9U2pQlY4OW+3XA==", "type": "package", "path": "volo.abp.eventbus/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.abppkg", "content/Volo.Abp.EventBus.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.dll", "lib/net8.0/Volo.Abp.EventBus.pdb", "lib/net8.0/Volo.Abp.EventBus.xml", "lib/netstandard2.0/Volo.Abp.EventBus.dll", "lib/netstandard2.0/Volo.Abp.EventBus.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.xml", "lib/netstandard2.1/Volo.Abp.EventBus.dll", "lib/netstandard2.1/Volo.Abp.EventBus.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.xml", "volo.abp.eventbus.8.3.4.nupkg.sha512", "volo.abp.eventbus.nuspec"]}, "Volo.Abp.EventBus.Abstractions/8.3.4": {"sha512": "kzYlFkxvU6hY+kV0VzBW6BzKnBc/5ZVsg42uNTuUDbxIOpJSEO9/6j0C1WVfmlqEpFhHVc6S9N7/jRBntPmXfw==", "type": "package", "path": "volo.abp.eventbus.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.EventBus.Abstractions.abppkg", "content/Volo.Abp.EventBus.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.EventBus.Abstractions.dll", "lib/net8.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/net8.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.EventBus.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.EventBus.Abstractions.xml", "volo.abp.eventbus.abstractions.8.3.4.nupkg.sha512", "volo.abp.eventbus.abstractions.nuspec"]}, "Volo.Abp.ExceptionHandling/8.3.4": {"sha512": "c2mwLOE6xfI86VTSjiS0bLW6Yan8F2EVjM3rTw0x9P9d/oEKFZ9kpnac16FxVub/hNxCwYkRESsDj4+oYzJ3Hg==", "type": "package", "path": "volo.abp.exceptionhandling/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ExceptionHandling.abppkg", "content/Volo.Abp.ExceptionHandling.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ExceptionHandling.dll", "lib/net8.0/Volo.Abp.ExceptionHandling.pdb", "lib/net8.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.0/Volo.Abp.ExceptionHandling.xml", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.dll", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.pdb", "lib/netstandard2.1/Volo.Abp.ExceptionHandling.xml", "volo.abp.exceptionhandling.8.3.4.nupkg.sha512", "volo.abp.exceptionhandling.nuspec"]}, "Volo.Abp.Features/8.3.4": {"sha512": "n8imibdHcaFYv3gYOc+JKdIQMLwZokFXs22rmvdysyjb4Pno5JTIvYhCi+bpBLE+wtqEomIdzGmuYtao66iWCA==", "type": "package", "path": "volo.abp.features/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Features.abppkg", "content/Volo.Abp.Features.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Features.dll", "lib/net8.0/Volo.Abp.Features.pdb", "lib/net8.0/Volo.Abp.Features.xml", "lib/netstandard2.0/Volo.Abp.Features.dll", "lib/netstandard2.0/Volo.Abp.Features.pdb", "lib/netstandard2.0/Volo.Abp.Features.xml", "lib/netstandard2.1/Volo.Abp.Features.dll", "lib/netstandard2.1/Volo.Abp.Features.pdb", "lib/netstandard2.1/Volo.Abp.Features.xml", "volo.abp.features.8.3.4.nupkg.sha512", "volo.abp.features.nuspec"]}, "Volo.Abp.GlobalFeatures/8.3.4": {"sha512": "SlUj6MB+sCmHat9D1gpy1p1Fy+QY5n+2NlyDwOgvE3PSFaR6+ytFQnSsjPGav6Vve+1CVuPVuDw3lkq2DkoipQ==", "type": "package", "path": "volo.abp.globalfeatures/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.GlobalFeatures.abppkg", "content/Volo.Abp.GlobalFeatures.abppkg.analyze.json", "lib/net8.0/Volo.Abp.GlobalFeatures.dll", "lib/net8.0/Volo.Abp.GlobalFeatures.pdb", "lib/net8.0/Volo.Abp.GlobalFeatures.xml", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.0/Volo.Abp.GlobalFeatures.xml", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.dll", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.pdb", "lib/netstandard2.1/Volo.Abp.GlobalFeatures.xml", "volo.abp.globalfeatures.8.3.4.nupkg.sha512", "volo.abp.globalfeatures.nuspec"]}, "Volo.Abp.Guids/8.3.4": {"sha512": "H1ZpXblbCotZ43bwhJQ7t6g3DjVh4k/QKJIhBwMHk2Q2qLFfceYfBbUuY/DGhcmVTxcZ4/vKuj1983TDHvrjCA==", "type": "package", "path": "volo.abp.guids/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Guids.abppkg", "content/Volo.Abp.Guids.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Guids.dll", "lib/net8.0/Volo.Abp.Guids.pdb", "lib/net8.0/Volo.Abp.Guids.xml", "lib/netstandard2.0/Volo.Abp.Guids.dll", "lib/netstandard2.0/Volo.Abp.Guids.pdb", "lib/netstandard2.0/Volo.Abp.Guids.xml", "lib/netstandard2.1/Volo.Abp.Guids.dll", "lib/netstandard2.1/Volo.Abp.Guids.pdb", "lib/netstandard2.1/Volo.Abp.Guids.xml", "volo.abp.guids.8.3.4.nupkg.sha512", "volo.abp.guids.nuspec"]}, "Volo.Abp.Http/8.3.4": {"sha512": "QFECTIsMNdscWWXwx3et1ooN5Oy5/UDxltN2Mu1HIm+29r/GLrbKex89IcyISQ985wStxs7Hx787en3EmqpwaQ==", "type": "package", "path": "volo.abp.http/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.abppkg", "content/Volo.Abp.Http.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.dll", "lib/net8.0/Volo.Abp.Http.pdb", "lib/net8.0/Volo.Abp.Http.xml", "lib/netstandard2.0/Volo.Abp.Http.dll", "lib/netstandard2.0/Volo.Abp.Http.pdb", "lib/netstandard2.0/Volo.Abp.Http.xml", "lib/netstandard2.1/Volo.Abp.Http.dll", "lib/netstandard2.1/Volo.Abp.Http.pdb", "lib/netstandard2.1/Volo.Abp.Http.xml", "volo.abp.http.8.3.4.nupkg.sha512", "volo.abp.http.nuspec"]}, "Volo.Abp.Http.Abstractions/8.3.4": {"sha512": "QAUwpjwXd7JjlWBOpMIxY1hcqC0bNQzFtgsmVgcd7qSXhnhowpPwXFBHO8BdT1lrqF2he/DK2RtjPh4sZwCeew==", "type": "package", "path": "volo.abp.http.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Http.Abstractions.abppkg", "content/Volo.Abp.Http.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Http.Abstractions.dll", "lib/net8.0/Volo.Abp.Http.Abstractions.pdb", "lib/net8.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Http.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Http.Abstractions.xml", "volo.abp.http.abstractions.8.3.4.nupkg.sha512", "volo.abp.http.abstractions.nuspec"]}, "Volo.Abp.Json/8.3.4": {"sha512": "Di3ZykflMInW5GgpTstp/BuOq/Bn4PgVThWlCDYVeCEoCkSkkwWs8BcCuo0BKO2L0TvmaNif5Vg+CvLCO10Upw==", "type": "package", "path": "volo.abp.json/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.abppkg", "content/Volo.Abp.Json.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.dll", "lib/net8.0/Volo.Abp.Json.pdb", "lib/net8.0/Volo.Abp.Json.xml", "lib/netstandard2.0/Volo.Abp.Json.dll", "lib/netstandard2.0/Volo.Abp.Json.pdb", "lib/netstandard2.0/Volo.Abp.Json.xml", "lib/netstandard2.1/Volo.Abp.Json.dll", "lib/netstandard2.1/Volo.Abp.Json.pdb", "lib/netstandard2.1/Volo.Abp.Json.xml", "volo.abp.json.8.3.4.nupkg.sha512", "volo.abp.json.nuspec"]}, "Volo.Abp.Json.Abstractions/8.3.4": {"sha512": "Wrg0eGBzdeMx0l0FJU02K7UNUJCp0J2Aj2lreH7euo8obWCsYEygabcueRuLXjxUZ8HusrtZvXTc13JsKnQtdA==", "type": "package", "path": "volo.abp.json.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.Abstractions.abppkg", "content/Volo.Abp.Json.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.Abstractions.dll", "lib/net8.0/Volo.Abp.Json.Abstractions.pdb", "lib/net8.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Json.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Json.Abstractions.xml", "volo.abp.json.abstractions.8.3.4.nupkg.sha512", "volo.abp.json.abstractions.nuspec"]}, "Volo.Abp.Json.SystemTextJson/8.3.4": {"sha512": "sGQ7iU2v7kG/YJgWi5oUKHDuyb44JDJdqRBFDzalwnk/duqGp6BPzC3o1vQjVkyAXdQSfW1Sqvmy/nx3DnqxQA==", "type": "package", "path": "volo.abp.json.systemtextjson/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Json.SystemTextJson.abppkg", "content/Volo.Abp.Json.SystemTextJson.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Json.SystemTextJson.dll", "lib/net8.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/net8.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.0/Volo.Abp.Json.SystemTextJson.xml", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.dll", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.pdb", "lib/netstandard2.1/Volo.Abp.Json.SystemTextJson.xml", "volo.abp.json.systemtextjson.8.3.4.nupkg.sha512", "volo.abp.json.systemtextjson.nuspec"]}, "Volo.Abp.Localization/8.3.4": {"sha512": "lu8p6bGQ70N6+lebW12MAI5EnS3DjXwlYIz4cwX5MY/8iPMzVO4ogJdzMytUOLpaHEQV6iEOGfDHE6xP9hADbg==", "type": "package", "path": "volo.abp.localization/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.abppkg", "content/Volo.Abp.Localization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.dll", "lib/net8.0/Volo.Abp.Localization.pdb", "lib/net8.0/Volo.Abp.Localization.xml", "lib/netstandard2.0/Volo.Abp.Localization.dll", "lib/netstandard2.0/Volo.Abp.Localization.pdb", "lib/netstandard2.0/Volo.Abp.Localization.xml", "lib/netstandard2.1/Volo.Abp.Localization.dll", "lib/netstandard2.1/Volo.Abp.Localization.pdb", "lib/netstandard2.1/Volo.Abp.Localization.xml", "volo.abp.localization.8.3.4.nupkg.sha512", "volo.abp.localization.nuspec"]}, "Volo.Abp.Localization.Abstractions/8.3.4": {"sha512": "qLejd/5Y+3BJWn3M/0XSX4WI5yg1LIqrcuQ5eVRx2DCxf024Fk9drwmrB0s2YnarEcsTxBq5yoHKgIuvrO4jBA==", "type": "package", "path": "volo.abp.localization.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Localization.Abstractions.abppkg", "content/Volo.Abp.Localization.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Localization.Abstractions.dll", "lib/net8.0/Volo.Abp.Localization.Abstractions.pdb", "lib/net8.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Localization.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Localization.Abstractions.xml", "volo.abp.localization.abstractions.8.3.4.nupkg.sha512", "volo.abp.localization.abstractions.nuspec"]}, "Volo.Abp.Minify/8.3.4": {"sha512": "sYLCYzkD1W18nuzv38XLiN/hB0i5iniQfObFrk/FiNGxIw+eDZeUpsxFdSLcJnv9zEhy46/jtZoT4Snd7PqRMQ==", "type": "package", "path": "volo.abp.minify/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Minify.abppkg", "content/Volo.Abp.Minify.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Minify.dll", "lib/net8.0/Volo.Abp.Minify.pdb", "lib/net8.0/Volo.Abp.Minify.xml", "lib/netstandard2.0/Volo.Abp.Minify.dll", "lib/netstandard2.0/Volo.Abp.Minify.pdb", "lib/netstandard2.0/Volo.Abp.Minify.xml", "lib/netstandard2.1/Volo.Abp.Minify.dll", "lib/netstandard2.1/Volo.Abp.Minify.pdb", "lib/netstandard2.1/Volo.Abp.Minify.xml", "volo.abp.minify.8.3.4.nupkg.sha512", "volo.abp.minify.nuspec"]}, "Volo.Abp.MultiTenancy/8.3.4": {"sha512": "BJsWGMqTq4jdZqskb8vZoUT2lyJM/MhKiqbpZcgFOZVj+S/QvjyGh8QELiJjpBKfg1N1PHFTZGzeG4luygXHAg==", "type": "package", "path": "volo.abp.multitenancy/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.MultiTenancy.abppkg", "content/Volo.Abp.MultiTenancy.abppkg.analyze.json", "lib/net8.0/Volo.Abp.MultiTenancy.dll", "lib/net8.0/Volo.Abp.MultiTenancy.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.xml", "volo.abp.multitenancy.8.3.4.nupkg.sha512", "volo.abp.multitenancy.nuspec"]}, "Volo.Abp.MultiTenancy.Abstractions/8.3.4": {"sha512": "sJ+oTbXJWpqn91YHmguBeuJHij/OE+R74S5uLew8ktvLafHb5AT0GP4hgQ48dgiYWRuoT1G38fzmN65AeyKB6Q==", "type": "package", "path": "volo.abp.multitenancy.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.MultiTenancy.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.MultiTenancy.Abstractions.xml", "volo.abp.multitenancy.abstractions.8.3.4.nupkg.sha512", "volo.abp.multitenancy.abstractions.nuspec"]}, "Volo.Abp.ObjectExtending/8.3.4": {"sha512": "aDh5iy87O+qMuuXc0WMFQYANh1zpyJnACGa48EXfZOfCy3q+DSJdfPZvm0SuIyeSW9ZJSeEkl16r/bfGrqjTlA==", "type": "package", "path": "volo.abp.objectextending/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ObjectExtending.abppkg", "content/Volo.Abp.ObjectExtending.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ObjectExtending.dll", "lib/net8.0/Volo.Abp.ObjectExtending.pdb", "lib/net8.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.0/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.0/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.0/Volo.Abp.ObjectExtending.xml", "lib/netstandard2.1/Volo.Abp.ObjectExtending.dll", "lib/netstandard2.1/Volo.Abp.ObjectExtending.pdb", "lib/netstandard2.1/Volo.Abp.ObjectExtending.xml", "volo.abp.objectextending.8.3.4.nupkg.sha512", "volo.abp.objectextending.nuspec"]}, "Volo.Abp.ObjectMapping/8.3.4": {"sha512": "qB8XYJaFxrzEXy5Efegkts8gaOYVvIzXwcuntBxAbxJCW/R7eKRj/+QJWQQA82lVzdgMrW7Z3OFnY1x9NNpTiQ==", "type": "package", "path": "volo.abp.objectmapping/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.ObjectMapping.abppkg", "content/Volo.Abp.ObjectMapping.abppkg.analyze.json", "lib/net8.0/Volo.Abp.ObjectMapping.dll", "lib/net8.0/Volo.Abp.ObjectMapping.pdb", "lib/net8.0/Volo.Abp.ObjectMapping.xml", "lib/netstandard2.0/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.0/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.0/Volo.Abp.ObjectMapping.xml", "lib/netstandard2.1/Volo.Abp.ObjectMapping.dll", "lib/netstandard2.1/Volo.Abp.ObjectMapping.pdb", "lib/netstandard2.1/Volo.Abp.ObjectMapping.xml", "volo.abp.objectmapping.8.3.4.nupkg.sha512", "volo.abp.objectmapping.nuspec"]}, "Volo.Abp.Security/8.3.4": {"sha512": "A+zocxt9HegZVtP83Dzsw6A2w2gfduVttz3maDrhgfrs92qOVxlncdTn+V6IZ6pabTmdEp6zP0rs6Zdql366Bg==", "type": "package", "path": "volo.abp.security/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Security.abppkg", "content/Volo.Abp.Security.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Security.dll", "lib/net8.0/Volo.Abp.Security.pdb", "lib/net8.0/Volo.Abp.Security.xml", "lib/netstandard2.0/Volo.Abp.Security.dll", "lib/netstandard2.0/Volo.Abp.Security.pdb", "lib/netstandard2.0/Volo.Abp.Security.xml", "lib/netstandard2.1/Volo.Abp.Security.dll", "lib/netstandard2.1/Volo.Abp.Security.pdb", "lib/netstandard2.1/Volo.Abp.Security.xml", "volo.abp.security.8.3.4.nupkg.sha512", "volo.abp.security.nuspec"]}, "Volo.Abp.Serialization/8.3.4": {"sha512": "zGQawUZcEdBjVF2aAhbQ+c9EFuiTxI5S5pxDUDehN6+WLIDfuz+qf3O49iz3c8O1jcSRdxgjMtWfsiq6obc0wg==", "type": "package", "path": "volo.abp.serialization/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Serialization.abppkg", "content/Volo.Abp.Serialization.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Serialization.dll", "lib/net8.0/Volo.Abp.Serialization.pdb", "lib/net8.0/Volo.Abp.Serialization.xml", "lib/netstandard2.0/Volo.Abp.Serialization.dll", "lib/netstandard2.0/Volo.Abp.Serialization.pdb", "lib/netstandard2.0/Volo.Abp.Serialization.xml", "lib/netstandard2.1/Volo.Abp.Serialization.dll", "lib/netstandard2.1/Volo.Abp.Serialization.pdb", "lib/netstandard2.1/Volo.Abp.Serialization.xml", "volo.abp.serialization.8.3.4.nupkg.sha512", "volo.abp.serialization.nuspec"]}, "Volo.Abp.Settings/8.3.4": {"sha512": "pbTQZw/RTVJK+2ufcYPG61FkJn/dybSUVpFAb2Bt6NxWj7D9QDBxgYMNocIimYsOoNRRBpHB/v0HgSEFzcZTkg==", "type": "package", "path": "volo.abp.settings/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Settings.abppkg", "content/Volo.Abp.Settings.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Settings.dll", "lib/net8.0/Volo.Abp.Settings.pdb", "lib/net8.0/Volo.Abp.Settings.xml", "lib/netstandard2.0/Volo.Abp.Settings.dll", "lib/netstandard2.0/Volo.Abp.Settings.pdb", "lib/netstandard2.0/Volo.Abp.Settings.xml", "lib/netstandard2.1/Volo.Abp.Settings.dll", "lib/netstandard2.1/Volo.Abp.Settings.pdb", "lib/netstandard2.1/Volo.Abp.Settings.xml", "volo.abp.settings.8.3.4.nupkg.sha512", "volo.abp.settings.nuspec"]}, "Volo.Abp.Specifications/8.3.4": {"sha512": "iurrAYlDIC+kCI8Mj6vF9sd3qCyyEtljvJBWt4Ysrh50jZzO9jReUisHhw9yzCu6ZNz7g//cbpsXnO1ebJTVvg==", "type": "package", "path": "volo.abp.specifications/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Specifications.abppkg", "content/Volo.Abp.Specifications.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Specifications.dll", "lib/net8.0/Volo.Abp.Specifications.pdb", "lib/net8.0/Volo.Abp.Specifications.xml", "lib/netstandard2.0/Volo.Abp.Specifications.dll", "lib/netstandard2.0/Volo.Abp.Specifications.pdb", "lib/netstandard2.0/Volo.Abp.Specifications.xml", "lib/netstandard2.1/Volo.Abp.Specifications.dll", "lib/netstandard2.1/Volo.Abp.Specifications.pdb", "lib/netstandard2.1/Volo.Abp.Specifications.xml", "volo.abp.specifications.8.3.4.nupkg.sha512", "volo.abp.specifications.nuspec"]}, "Volo.Abp.Swashbuckle/8.3.4": {"sha512": "fAVSicW53Zad5hw8o4CvSOS92QRxY8vuPpEktFyUmF4PfP5JS9wmm7T36I2SenmVVVHLoC3M7/rCeLR6PZcbRg==", "type": "package", "path": "volo.abp.swashbuckle/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Swashbuckle.abppkg", "content/Volo.Abp.Swashbuckle.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Swashbuckle.dll", "lib/net8.0/Volo.Abp.Swashbuckle.pdb", "lib/net8.0/Volo.Abp.Swashbuckle.xml", "volo.abp.swashbuckle.8.3.4.nupkg.sha512", "volo.abp.swashbuckle.nuspec"]}, "Volo.Abp.Threading/8.3.4": {"sha512": "R4bZmjAl8VwvctLFaM92NCLUPok3AnokEMJqI5J8aoDtaPdJ9T6YndJjkMaeKekckEYLp3UcRxt6ytViD6f8eQ==", "type": "package", "path": "volo.abp.threading/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Threading.abppkg", "content/Volo.Abp.Threading.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Threading.dll", "lib/net8.0/Volo.Abp.Threading.pdb", "lib/net8.0/Volo.Abp.Threading.xml", "lib/netstandard2.0/Volo.Abp.Threading.dll", "lib/netstandard2.0/Volo.Abp.Threading.pdb", "lib/netstandard2.0/Volo.Abp.Threading.xml", "lib/netstandard2.1/Volo.Abp.Threading.dll", "lib/netstandard2.1/Volo.Abp.Threading.pdb", "lib/netstandard2.1/Volo.Abp.Threading.xml", "volo.abp.threading.8.3.4.nupkg.sha512", "volo.abp.threading.nuspec"]}, "Volo.Abp.Timing/8.3.4": {"sha512": "SbT6QM2v04J6CjEqy1Ubgh/uZvdVufghFZtgVJkTYQebMZYLCxkHPga5buYGt9Hw8bhhmFmGtidXznJBWbkHOQ==", "type": "package", "path": "volo.abp.timing/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Timing.abppkg", "content/Volo.Abp.Timing.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Timing.dll", "lib/net8.0/Volo.Abp.Timing.pdb", "lib/net8.0/Volo.Abp.Timing.xml", "lib/netstandard2.0/Volo.Abp.Timing.dll", "lib/netstandard2.0/Volo.Abp.Timing.pdb", "lib/netstandard2.0/Volo.Abp.Timing.xml", "lib/netstandard2.1/Volo.Abp.Timing.dll", "lib/netstandard2.1/Volo.Abp.Timing.pdb", "lib/netstandard2.1/Volo.Abp.Timing.xml", "volo.abp.timing.8.3.4.nupkg.sha512", "volo.abp.timing.nuspec"]}, "Volo.Abp.UI/8.3.4": {"sha512": "Sq2VbZE0DafA97kt7q5Jym4mGu2cB7MUJVaqujU4oB41Bxb8mz3id2ExrDezbACgl8rh04FvH12YGXTPCGhHyA==", "type": "package", "path": "volo.abp.ui/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.UI.abppkg", "content/Volo.Abp.UI.abppkg.analyze.json", "lib/net8.0/Volo.Abp.UI.dll", "lib/net8.0/Volo.Abp.UI.pdb", "lib/net8.0/Volo.Abp.UI.xml", "lib/netstandard2.0/Volo.Abp.UI.dll", "lib/netstandard2.0/Volo.Abp.UI.pdb", "lib/netstandard2.0/Volo.Abp.UI.xml", "lib/netstandard2.1/Volo.Abp.UI.dll", "lib/netstandard2.1/Volo.Abp.UI.pdb", "lib/netstandard2.1/Volo.Abp.UI.xml", "volo.abp.ui.8.3.4.nupkg.sha512", "volo.abp.ui.nuspec"]}, "Volo.Abp.UI.Navigation/8.3.4": {"sha512": "eimZU0F9Tl3ShW93ay1VsD3ZoOFF0LBvTX31Grm4KfOyp0JgXSZBxj1k95c4/RgXNpo+05SUEZpyPRl0VY2T9g==", "type": "package", "path": "volo.abp.ui.navigation/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.UI.Navigation.abppkg", "content/Volo.Abp.UI.Navigation.abppkg.analyze.json", "lib/net8.0/Volo.Abp.UI.Navigation.dll", "lib/net8.0/Volo.Abp.UI.Navigation.pdb", "lib/net8.0/Volo.Abp.UI.Navigation.xml", "lib/netstandard2.0/Volo.Abp.UI.Navigation.dll", "lib/netstandard2.0/Volo.Abp.UI.Navigation.pdb", "lib/netstandard2.0/Volo.Abp.UI.Navigation.xml", "lib/netstandard2.1/Volo.Abp.UI.Navigation.dll", "lib/netstandard2.1/Volo.Abp.UI.Navigation.pdb", "lib/netstandard2.1/Volo.Abp.UI.Navigation.xml", "volo.abp.ui.navigation.8.3.4.nupkg.sha512", "volo.abp.ui.navigation.nuspec"]}, "Volo.Abp.Uow/8.3.4": {"sha512": "KSqhpoyztXEXJ5YrchpbUxydWT045TIBR74yyJo0YFIFLL7VwXNPBesWAIoJcXRG+A/O38DABsiC1Dw6NbQ9QQ==", "type": "package", "path": "volo.abp.uow/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Uow.abppkg", "content/Volo.Abp.Uow.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Uow.dll", "lib/net8.0/Volo.Abp.Uow.pdb", "lib/net8.0/Volo.Abp.Uow.xml", "lib/netstandard2.0/Volo.Abp.Uow.dll", "lib/netstandard2.0/Volo.Abp.Uow.pdb", "lib/netstandard2.0/Volo.Abp.Uow.xml", "lib/netstandard2.1/Volo.Abp.Uow.dll", "lib/netstandard2.1/Volo.Abp.Uow.pdb", "lib/netstandard2.1/Volo.Abp.Uow.xml", "volo.abp.uow.8.3.4.nupkg.sha512", "volo.abp.uow.nuspec"]}, "Volo.Abp.Validation/8.3.4": {"sha512": "kIpcTqM49xpT2ELV3UIqVYwsNLVTY36Q5Gf7+PYyOzYyB/4Gx6vKv2Zx2/J3XTEZTVoGeOoN7Nuxy6NLeW1MpQ==", "type": "package", "path": "volo.abp.validation/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.abppkg", "content/Volo.Abp.Validation.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.dll", "lib/net8.0/Volo.Abp.Validation.pdb", "lib/net8.0/Volo.Abp.Validation.xml", "lib/netstandard2.0/Volo.Abp.Validation.dll", "lib/netstandard2.0/Volo.Abp.Validation.pdb", "lib/netstandard2.0/Volo.Abp.Validation.xml", "lib/netstandard2.1/Volo.Abp.Validation.dll", "lib/netstandard2.1/Volo.Abp.Validation.pdb", "lib/netstandard2.1/Volo.Abp.Validation.xml", "volo.abp.validation.8.3.4.nupkg.sha512", "volo.abp.validation.nuspec"]}, "Volo.Abp.Validation.Abstractions/8.3.4": {"sha512": "/ucmdGyc0Gvt75LrVv0gni1q2fFy4ze0SX8ndmuLkFk7QCERmWI5Ui818ZrOYWanxdj8b8OyFY3UOSL6RDnK9w==", "type": "package", "path": "volo.abp.validation.abstractions/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.Validation.Abstractions.abppkg", "content/Volo.Abp.Validation.Abstractions.abppkg.analyze.json", "lib/net8.0/Volo.Abp.Validation.Abstractions.dll", "lib/net8.0/Volo.Abp.Validation.Abstractions.pdb", "lib/net8.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.0/Volo.Abp.Validation.Abstractions.xml", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.dll", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.pdb", "lib/netstandard2.1/Volo.Abp.Validation.Abstractions.xml", "volo.abp.validation.abstractions.8.3.4.nupkg.sha512", "volo.abp.validation.abstractions.nuspec"]}, "Volo.Abp.VirtualFileSystem/8.3.4": {"sha512": "uPpDF04O0PfwrjH65dG61dFW/JP8vJRTRscRpvJpGGg4/QDStyTIaa2bESVy5DdemqBsI6gzgOYe5YxI9DlYjA==", "type": "package", "path": "volo.abp.virtualfilesystem/8.3.4", "files": [".nupkg.metadata", ".signature.p7s", "NuGet.md", "content/Volo.Abp.VirtualFileSystem.abppkg", "content/Volo.Abp.VirtualFileSystem.abppkg.analyze.json", "lib/net8.0/Volo.Abp.VirtualFileSystem.dll", "lib/net8.0/Volo.Abp.VirtualFileSystem.pdb", "lib/net8.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.0/Volo.Abp.VirtualFileSystem.xml", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.dll", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.pdb", "lib/netstandard2.1/Volo.Abp.VirtualFileSystem.xml", "volo.abp.virtualfilesystem.8.3.4.nupkg.sha512", "volo.abp.virtualfilesystem.nuspec"]}, "YamlDotNet/13.1.1": {"sha512": "3AS0fEwxOucP0h68JXMxfj3NyfgX9EOVE29NlKqKxWMHfKMAuZ7MWw5u362Bs6OALtnBTzi/JYIiMidQXoKVWw==", "type": "package", "path": "yamldotnet/13.1.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "images/yamldotnet.png", "lib/net35/YamlDotNet.dll", "lib/net35/YamlDotNet.xml", "lib/net45/YamlDotNet.dll", "lib/net45/YamlDotNet.xml", "lib/net47/YamlDotNet.dll", "lib/net47/YamlDotNet.xml", "lib/net6.0/YamlDotNet.dll", "lib/net6.0/YamlDotNet.xml", "lib/net7.0/YamlDotNet.dll", "lib/net7.0/YamlDotNet.xml", "lib/netstandard2.0/YamlDotNet.dll", "lib/netstandard2.0/YamlDotNet.xml", "lib/netstandard2.1/YamlDotNet.dll", "lib/netstandard2.1/YamlDotNet.xml", "yamldotnet.13.1.1.nupkg.sha512", "yamldotnet.nuspec"]}, "Pe.Application/1.0.0": {"type": "project", "path": "../Pe.Application/Pe.Application.csproj", "msbuildProject": "../Pe.Application/Pe.Application.csproj"}, "Pe.Application.Contracts/1.0.0": {"type": "project", "path": "../Pe.Application.Contracts/Pe.Application.Contracts.csproj", "msbuildProject": "../Pe.Application.Contracts/Pe.Application.Contracts.csproj"}, "Pe.Domain/1.0.0": {"type": "project", "path": "../Pe.Domain/Pe.Domain.csproj", "msbuildProject": "../Pe.Domain/Pe.Domain.csproj"}, "Pe.Domain.Shared/1.0.0": {"type": "project", "path": "../Pe.Domain.Shared/Pe.Domain.Shared.csproj", "msbuildProject": "../Pe.Domain.Shared/Pe.Domain.Shared.csproj"}, "Pe.SqlSugarCore/1.0.0": {"type": "project", "path": "../Pe.SqlSugarCore/Pe.SqlSugarCore.csproj", "msbuildProject": "../Pe.SqlSugarCore/Pe.SqlSugarCore.csproj"}}, "projectFileDependencyGroups": {"net8.0": ["Pe.Application >= 1.0.0", "Pe.SqlSugarCore >= 1.0.0", "Volo.Abp.AspNetCore.Mvc >= 8.3.4", "Volo.Abp.Autofac >= 8.3.4", "Volo.Abp.Swashbuckle >= 8.3.4"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj", "projectName": "Pe.WebApi", "projectPath": "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.WebApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj"}, "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[8.3.4, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.3.4, )"}, "Volo.Abp.Swashbuckle": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net8.0”还原包“SqlSugar *********”。此包可能与项目不完全兼容。", "libraryId": "SqlSugar", "targetGraphs": ["net8.0"]}]}