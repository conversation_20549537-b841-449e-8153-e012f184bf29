[{"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApiExploring.AbpApiDefinitionController", "Method": "Get", "RelativePath": "api/abp/api-definition", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeTypes", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Http.Modeling.ApplicationApiDescriptionModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationConfigurationController", "Method": "GetAsync", "RelativePath": "api/abp/application-configuration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "IncludeLocalizationResources", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationConfigurationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.AbpApplicationLocalizationController", "Method": "GetAsync", "RelativePath": "api/abp/application-localization", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "CultureName", "Type": "System.String", "IsRequired": false}, {"Name": "OnlyDynamics", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.AspNetCore.Mvc.ApplicationConfigurations.ApplicationLocalizationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Pe.Application.BookService", "Method": "GetListAsync", "RelativePath": "api/app/book", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Sorting", "Type": "System.String", "IsRequired": false}, {"Name": "SkipCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "MaxResultCount", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Volo.Abp.Application.Dtos.PagedResultDto`1[[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Application.Services.AbstractKeyCrudAppService`7[[Pe.Domain.Entities.BookAggregateRoot, Pe.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts, Version=8.3.4.0, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "Method": "CreateAsync", "RelativePath": "api/app/book", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "input", "Type": "Pe.Application.Contracts.Dtos.BookDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Pe.Application.Contracts.Dtos.BookDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Application.Services.AbstractKeyCrudAppService`7[[Pe.Domain.Entities.BookAggregateRoot, Pe.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts, Version=8.3.4.0, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "Method": "UpdateAsync", "RelativePath": "api/app/book/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "input", "Type": "Pe.Application.Contracts.Dtos.BookDto", "IsRequired": true}], "ReturnTypes": [{"Type": "Pe.Application.Contracts.Dtos.BookDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Application.Services.AbstractKeyCrudAppService`7[[Pe.Domain.Entities.BookAggregateRoot, Pe.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts, Version=8.3.4.0, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null]]", "Method": "DeleteAsync", "RelativePath": "api/app/book/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Volo.Abp.Application.Services.AbstractKeyReadOnlyAppService`5[[Pe.Domain.Entities.BookAggregateRoot, Pe.Domain, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[Pe.Application.Contracts.Dtos.BookDto, Pe.Application.Contracts, Version=*******, Culture=neutral, PublicKeyToken=null],[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[Volo.Abp.Application.Dtos.PagedAndSortedResultRequestDto, Volo.Abp.Ddd.Application.Contracts, Version=8.3.4.0, Culture=neutral, PublicKeyToken=null]]", "Method": "GetAsync", "RelativePath": "api/app/book/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Pe.Application.Contracts.Dtos.BookDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Pe.Application.BookService", "Method": "GetHelloWorld", "RelativePath": "api/app/book/hello-world", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 501}, {"Type": "Volo.Abp.Http.RemoteServiceErrorResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 500}]}, {"ContainingType": "Pe.WebApi.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Pe.WebApi.WeatherForecast, Pe.WebApi, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]