using SqlSugar;
using System.Linq.Expressions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Linq;

namespace Pe.SqlSugarCore.Repositories;

public class SqlSugarRepositoryBase<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : class, IEntity<TKey>, new()
{
    protected readonly ISqlSugarClient _db;

    public SqlSugarRepositoryBase(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<TEntity> InsertAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        var result = await _db.Insertable(entity).ExecuteReturnEntityAsync();
        return result;
    }

    public async Task InsertManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        foreach (var entity in entities)
        {
            await _db.Insertable(entity).ExecuteCommandAsync();
        }
    }

    public async Task<TEntity> UpdateAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _db.Updateable(entity).ExecuteCommandAsync();
        return entity;
    }

    public async Task UpdateManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        foreach (var entity in entities)
        {
            await _db.Updateable(entity).ExecuteCommandAsync();
        }
    }

    public async Task DeleteAsync(TKey id, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _db.Deleteable<TEntity>().In(id).ExecuteCommandAsync();
    }

    public async Task DeleteAsync(TEntity entity, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _db.Deleteable(entity).ExecuteCommandAsync();
    }

    public async Task DeleteManyAsync(IEnumerable<TEntity> entities, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        foreach (var entity in entities)
        {
            await _db.Deleteable(entity).ExecuteCommandAsync();
        }
    }

    public async Task DeleteManyAsync(IEnumerable<TKey> ids, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _db.Deleteable<TEntity>().In(ids.ToArray()).ExecuteCommandAsync();
    }

    public async Task DeleteAsync(Expression<Func<TEntity, bool>> predicate, bool autoSave = false, CancellationToken cancellationToken = default)
    {
        await _db.Deleteable<TEntity>().Where(predicate).ExecuteCommandAsync();
    }

    public async Task DeleteDirectAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        await _db.Deleteable<TEntity>().Where(predicate).ExecuteCommandAsync();
    }

    public async Task<TEntity?> FindAsync(TKey id, bool includeDetails = true, CancellationToken cancellationToken = default)
    {
        return await _db.Queryable<TEntity>().InSingleAsync(id);
    }

    public async Task<TEntity?> FindAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default)
    {
        return await _db.Queryable<TEntity>().Where(predicate).FirstAsync();
    }

    public async Task<TEntity> GetAsync(TKey id, bool includeDetails = true, CancellationToken cancellationToken = default)
    {
        var entity = await FindAsync(id, includeDetails, cancellationToken);
        if (entity == null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }
        return entity;
    }

    public async Task<TEntity> GetAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = true, CancellationToken cancellationToken = default)
    {
        var entity = await FindAsync(predicate, includeDetails, cancellationToken);
        if (entity == null)
        {
            throw new EntityNotFoundException(typeof(TEntity));
        }
        return entity;
    }

    public async Task<List<TEntity>> GetListAsync(bool includeDetails = false, CancellationToken cancellationToken = default)
    {
        return await _db.Queryable<TEntity>().ToListAsync();
    }

    public async Task<List<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> predicate, bool includeDetails = false, CancellationToken cancellationToken = default)
    {
        return await _db.Queryable<TEntity>().Where(predicate).ToListAsync();
    }

    public async Task<long> GetCountAsync(CancellationToken cancellationToken = default)
    {
        return await _db.Queryable<TEntity>().CountAsync();
    }

    public async Task<List<TEntity>> GetPagedListAsync(int skipCount, int maxResultCount, string sorting, bool includeDetails = false, CancellationToken cancellationToken = default)
    {
        var query = _db.Queryable<TEntity>();
        
        if (!string.IsNullOrEmpty(sorting))
        {
            query = query.OrderBy(sorting);
        }
        
        return await query.Skip(skipCount).Take(maxResultCount).ToListAsync();
    }

    #region IQueryable Support
    public IQueryable<TEntity> WithDetails()
    {
        return _db.Queryable<TEntity>().ToList().AsQueryable();
    }

    public IQueryable<TEntity> WithDetails(params Expression<Func<TEntity, object>>[] propertySelectors)
    {
        return WithDetails();
    }

    public Task<IQueryable<TEntity>> WithDetailsAsync()
    {
        return Task.FromResult(WithDetails());
    }

    public Task<IQueryable<TEntity>> WithDetailsAsync(params Expression<Func<TEntity, object>>[] propertySelectors)
    {
        return Task.FromResult(WithDetails(propertySelectors));
    }

    public Task<IQueryable<TEntity>> GetQueryableAsync()
    {
        return Task.FromResult(_db.Queryable<TEntity>().ToList().AsQueryable());
    }
    #endregion

    #region Sync Methods (implemented as async calls)
    public TEntity Insert(TEntity entity, bool autoSave = false)
    {
        return InsertAsync(entity, autoSave).GetAwaiter().GetResult();
    }

    public void InsertMany(IEnumerable<TEntity> entities, bool autoSave = false)
    {
        InsertManyAsync(entities, autoSave).GetAwaiter().GetResult();
    }

    public TEntity Update(TEntity entity, bool autoSave = false)
    {
        return UpdateAsync(entity, autoSave).GetAwaiter().GetResult();
    }

    public void UpdateMany(IEnumerable<TEntity> entities, bool autoSave = false)
    {
        UpdateManyAsync(entities, autoSave).GetAwaiter().GetResult();
    }

    public void Delete(TKey id, bool autoSave = false)
    {
        DeleteAsync(id, autoSave).GetAwaiter().GetResult();
    }

    public void Delete(TEntity entity, bool autoSave = false)
    {
        DeleteAsync(entity, autoSave).GetAwaiter().GetResult();
    }

    public void DeleteMany(IEnumerable<TEntity> entities, bool autoSave = false)
    {
        DeleteManyAsync(entities, autoSave).GetAwaiter().GetResult();
    }

    public void DeleteMany(IEnumerable<TKey> ids, bool autoSave = false)
    {
        DeleteManyAsync(ids, autoSave).GetAwaiter().GetResult();
    }

    public TEntity? Find(TKey id, bool includeDetails = true)
    {
        return FindAsync(id, includeDetails).GetAwaiter().GetResult();
    }

    public TEntity Get(TKey id, bool includeDetails = true)
    {
        return GetAsync(id, includeDetails).GetAwaiter().GetResult();
    }

    public List<TEntity> GetList(bool includeDetails = false)
    {
        return GetListAsync(includeDetails).GetAwaiter().GetResult();
    }

    public List<TEntity> GetList(Expression<Func<TEntity, bool>> predicate, bool includeDetails = false)
    {
        return GetListAsync(predicate, includeDetails).GetAwaiter().GetResult();
    }

    public long GetCount()
    {
        return GetCountAsync().GetAwaiter().GetResult();
    }

    public List<TEntity> GetPagedList(int skipCount, int maxResultCount, string sorting, bool includeDetails = false)
    {
        return GetPagedListAsync(skipCount, maxResultCount, sorting, includeDetails).GetAwaiter().GetResult();
    }
    #endregion

    #region Additional Properties and Methods
    public IAsyncQueryableExecuter AsyncExecuter => throw new NotImplementedException("SqlSugar does not support IAsyncQueryableExecuter");

    public bool? IsChangeTrackingEnabled => false;
    #endregion
}
