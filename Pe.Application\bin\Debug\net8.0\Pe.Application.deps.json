{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Pe.Application/1.0.0": {"dependencies": {"Pe.Application.Contracts": "1.0.0", "Pe.Domain": "1.0.0", "Pe.SqlSugarCore": "1.0.0", "Volo.Abp.AutoMapper": "8.3.4", "Volo.Abp.Ddd.Application": "8.3.4"}, "runtime": {"Pe.Application.dll": {}}}, "AsyncKeyedLock/6.3.4": {"runtime": {"lib/net8.0/AsyncKeyedLock.dll": {"assemblyVersion": "6.3.4.0", "fileVersion": "6.3.4.0"}}}, "AutoMapper/13.0.1": {"dependencies": {"Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net6.0/AutoMapper.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.0"}}}, "JetBrains.Annotations/2023.3.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2023.3.0.0"}}}, "Microsoft.AspNetCore.Authorization/8.0.4": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.4", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.17014"}}}, "Microsoft.AspNetCore.Metadata/8.0.4": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.17014"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.4"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Embedded/8.0.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.424.17014"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Localization/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Localization.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "SqlSugar/*********": {"runtime": {"lib/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable/8.0.0": {}, "System.Data.SqlClient/4.8.6": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.700.23.52603"}}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.3.5": {"runtime": {"lib/net7.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "1.3.5.0", "fileVersion": "1.3.5.0"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.4": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "TimeZoneConverter/6.1.0": {"runtime": {"lib/net6.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing/8.3.4": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Threading": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Auditing.Contracts/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Auditing.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization/8.3.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Security": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Authorization.Abstractions/8.3.4": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Authorization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.AutoMapper/8.3.4": {"dependencies": {"AutoMapper": "13.0.1", "Volo.Abp.Auditing": "8.3.4", "Volo.Abp.ObjectExtending": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.AutoMapper.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.BackgroundWorkers/8.3.4": {"dependencies": {"Volo.Abp.Threading": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.BackgroundWorkers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Caching/8.3.4": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "8.0.0", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Serialization": "8.3.4", "Volo.Abp.Threading": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/8.3.4": {"dependencies": {"JetBrains.Annotations": "2023.3.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Localization": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Nito.AsyncEx.Context": "5.1.2", "System.Collections.Immutable": "8.0.0", "System.Linq.Dynamic.Core": "1.3.5", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net8.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Data/8.3.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.ObjectExtending": "8.3.4", "Volo.Abp.Uow": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application/8.3.4": {"dependencies": {"Volo.Abp.Authorization": "8.3.4", "Volo.Abp.Ddd.Application.Contracts": "8.3.4", "Volo.Abp.Ddd.Domain": "8.3.4", "Volo.Abp.Features": "8.3.4", "Volo.Abp.GlobalFeatures": "8.3.4", "Volo.Abp.Http.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Settings": "8.3.4", "Volo.Abp.Validation": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Application.Contracts/8.3.4": {"dependencies": {"Volo.Abp.Auditing.Contracts": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Application.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain/8.3.4": {"dependencies": {"Volo.Abp.Auditing": "8.3.4", "Volo.Abp.Caching": "8.3.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Ddd.Domain.Shared": "8.3.4", "Volo.Abp.EventBus": "8.3.4", "Volo.Abp.ExceptionHandling": "8.3.4", "Volo.Abp.Guids": "8.3.4", "Volo.Abp.ObjectMapping": "8.3.4", "Volo.Abp.Specifications": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Ddd.Domain.Shared/8.3.4": {"dependencies": {"Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Ddd.Domain.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.DistributedLocking.Abstractions/8.3.4": {"dependencies": {"AsyncKeyedLock": "6.3.4", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.DistributedLocking.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus/8.3.4": {"dependencies": {"Volo.Abp.BackgroundWorkers": "8.3.4", "Volo.Abp.DistributedLocking.Abstractions": "8.3.4", "Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.Guids": "8.3.4", "Volo.Abp.Json": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.EventBus.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.ObjectExtending": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.EventBus.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ExceptionHandling/8.3.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Embedded": "8.0.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.ExceptionHandling.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Features/8.3.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.MultiTenancy": "8.3.4", "Volo.Abp.Validation": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.GlobalFeatures/8.3.4": {"dependencies": {"Volo.Abp.Authorization.Abstractions": "8.3.4", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.GlobalFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Guids/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Guids.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Http.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json/8.3.4": {"dependencies": {"Volo.Abp.Json.SystemTextJson": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Json.SystemTextJson/8.3.4": {"dependencies": {"System.Text.Json": "8.0.4", "Volo.Abp.Data": "8.3.4", "Volo.Abp.Json.Abstractions": "8.3.4", "Volo.Abp.Timing": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Json.SystemTextJson.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization/8.3.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Settings": "8.3.4", "Volo.Abp.Threading": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Localization.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy/8.3.4": {"dependencies": {"Volo.Abp.Data": "8.3.4", "Volo.Abp.EventBus.Abstractions": "8.3.4", "Volo.Abp.MultiTenancy.Abstractions": "8.3.4", "Volo.Abp.Security": "8.3.4", "Volo.Abp.Settings": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.MultiTenancy.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.Localization": "8.3.4", "Volo.Abp.VirtualFileSystem": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.MultiTenancy.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectExtending/8.3.4": {"dependencies": {"Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Validation.Abstractions": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectExtending.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.ObjectMapping/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.ObjectMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Security/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Security.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Serialization/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Settings/8.3.4": {"dependencies": {"Volo.Abp.Data": "8.3.4", "Volo.Abp.Localization.Abstractions": "8.3.4", "Volo.Abp.Security": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Settings.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Specifications/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Specifications.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Threading/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Timing/8.3.4": {"dependencies": {"TimeZoneConverter": "6.1.0", "Volo.Abp.Localization": "8.3.4", "Volo.Abp.Settings": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Timing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Uow/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Uow.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation/8.3.4": {"dependencies": {"Volo.Abp.Localization": "8.3.4", "Volo.Abp.Validation.Abstractions": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Validation.Abstractions/8.3.4": {"dependencies": {"Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.Validation.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.VirtualFileSystem/8.3.4": {"dependencies": {"Microsoft.Extensions.FileProviders.Composite": "8.0.0", "Microsoft.Extensions.FileProviders.Embedded": "8.0.4", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Volo.Abp.Core": "8.3.4"}, "runtime": {"lib/net8.0/Volo.Abp.VirtualFileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pe.Application.Contracts/1.0.0": {"dependencies": {"Pe.Domain.Shared": "1.0.0", "Volo.Abp.Ddd.Application.Contracts": "8.3.4"}, "runtime": {"Pe.Application.Contracts.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Pe.Domain/1.0.0": {"dependencies": {"Pe.Domain.Shared": "1.0.0", "Volo.Abp.Ddd.Domain": "8.3.4"}, "runtime": {"Pe.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Pe.Domain.Shared/1.0.0": {"dependencies": {"Volo.Abp.Ddd.Domain.Shared": "8.3.4"}, "runtime": {"Pe.Domain.Shared.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Pe.SqlSugarCore/1.0.0": {"dependencies": {"Pe.Domain": "1.0.0", "SqlSugar": "*********", "System.Data.SqlClient": "4.8.6", "Volo.Abp.Ddd.Domain": "8.3.4"}, "runtime": {"Pe.SqlSugarCore.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"Pe.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AsyncKeyedLock/6.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-+0YPXvamnopzHSgBpv0vg+9/NJH+6jm1ZODuMMs7p43yH7hA/NxNi+/U7T7eazZqW+V+0ZsbreYruhu6i7N4PA==", "path": "asynckeyedlock/6.3.4", "hashPath": "asynckeyedlock.6.3.4.nupkg.sha512"}, "AutoMapper/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "path": "automapper/13.0.1", "hashPath": "automapper.13.0.1.nupkg.sha512"}, "JetBrains.Annotations/2023.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PHfnvdBUdGaTVG9bR/GEfxgTwWM0Z97Y6X3710wiljELBISipSfF5okn/vz+C2gfO+ihoEyVPjaJwn8ZalVukA==", "path": "jetbrains.annotations/2023.3.0", "hashPath": "jetbrains.annotations.2023.3.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dzYUHvqyavBiYVd5BZNAgV5rT+DC4HyIWPnbeYgxQNBYOFomvvwzPfidQf1Ld/HhkXhEj3tlzqNb4gy2P2ukUg==", "path": "microsoft.aspnetcore.authorization/8.0.4", "hashPath": "microsoft.aspnetcore.authorization.8.0.4.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-TG3JhI7dkDCIoxZ3TL/5yptLYLg56w1f4jE8GSYnZXSso1xY5JKKCxObK+xKQJU5kZir30+QUGX1WpbC1kDcow==", "path": "microsoft.aspnetcore.metadata/8.0.4", "hashPath": "microsoft.aspnetcore.metadata.8.0.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Composite/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ynMjdZ5B3Fd3A9GxJaNhIcTrjLY1bXDQltyVIMVOxbT0ssTOCpFYWc977bVBAocB62fYWu/RN6/1HLnX/HjVuQ==", "path": "microsoft.extensions.fileproviders.composite/8.0.0", "hashPath": "microsoft.extensions.fileproviders.composite.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Embedded/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-r3wpZ7RSjDqtMQmsIICjOrOylCnOlJJ0nWcnsuLb+iyLslBEe2+wHAI7xCmEMDu8ZP1K5qSryXH8Kt4o6Lyn9g==", "path": "microsoft.extensions.fileproviders.embedded/8.0.4", "hashPath": "microsoft.extensions.fileproviders.embedded.8.0.4.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-I4xyK9RaXyJBkU5+jQpkZITR/54tXGbdt5CsBmogf8Irrz+RXXsp1zJgrmQzxEOpxj5VMAA/GQ4SGZMeM5nVSg==", "path": "microsoft.extensions.localization/8.0.0", "hashPath": "microsoft.extensions.localization.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LIMzXjjzv7o3tx3XXvQVHNcrMNPfSoUp4R/YJucdyBixJGRp5p8v2rF1CnRbo+wwtzB4Se/gJYyUpNN8F/TMGw==", "path": "microsoft.extensions.localization.abstractions/8.0.0", "hashPath": "microsoft.extensions.localization.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "path": "microsoft.extensions.logging.abstractions/8.0.1", "hashPath": "microsoft.extensions.logging.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "SqlSugar/*********": {"type": "package", "serviceable": true, "sha512": "sha512-5BFK7HOBYu/DC0NawHHH+nGGF4Kft3P510nvQ84txm215M9D/ru/9AGlkEw2oOYmdJ+ZZstA5GDC33kPJiMniQ==", "path": "sqlsugar/*********", "hashPath": "sqlsugar.*********.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.6": {"type": "package", "serviceable": true, "sha512": "sha512-2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "path": "system.data.sqlclient/4.8.6", "hashPath": "system.data.sqlclient.4.8.6.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-G8aRAVHSQ/Jmt6dekX+z4K3L6ecX3CizaIYSQoDUolPRIPBYmBCabT+/pq5fMAFM57aOchFn8PUufuVfLjvEZQ==", "path": "system.linq.dynamic.core/1.3.5", "hashPath": "system.linq.dynamic.core.1.3.5.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-bAkhgDJ88XTsqczoxEMliSrpijKZHhbJQldhAmObj/RbrN3sU5dcokuXmWJWsdQAhiMJ9bTayWsL1C9fbbCRhw==", "path": "system.text.json/8.0.4", "hashPath": "system.text.json.8.0.4.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "TimeZoneConverter/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UGdtyKWJqXXinyvGB9X6NVoIYbTAidoZYmn3aXzxeEYC9+OL8vF36eDt1qjb6RqBkWDl4v7iE84ecI+dFhA80A==", "path": "timezoneconverter/6.1.0", "hashPath": "timezoneconverter.6.1.0.nupkg.sha512"}, "Volo.Abp.Auditing/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-QDQrhj5OaYRKGAOt305YSEl5VPQWBl+/J7EqJt/NeD2qPPSIVIOCEGN+zpXDwOxOF9/8cZeyzIV8SVE1Ln+6Yw==", "path": "volo.abp.auditing/8.3.4", "hashPath": "volo.abp.auditing.8.3.4.nupkg.sha512"}, "Volo.Abp.Auditing.Contracts/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-uw7U+gqLTcQJEhrk2pPsleTcQTV/ej7inBAfrAnYs4m4adAZzzCXtJKI4eqmjSWWwcZrQ1hwLOwKWFSuQr39Hw==", "path": "volo.abp.auditing.contracts/8.3.4", "hashPath": "volo.abp.auditing.contracts.8.3.4.nupkg.sha512"}, "Volo.Abp.Authorization/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-LwFhq4PmNDsekYrUm6LCYXJzNXYlxlXwfBnKMqwstiuUesr6IJEY5oOHvhRvZKPxaRjWTrDObtqFfJpnd/jHqg==", "path": "volo.abp.authorization/8.3.4", "hashPath": "volo.abp.authorization.8.3.4.nupkg.sha512"}, "Volo.Abp.Authorization.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-vG7ePbrh+Tyx9G/KgTnp4bmcKszDSsEZExbSl5YRO2pW8OgTDCZ4USjnH8VHG0LE5I+sIlL8IWCvkhfExTX4Og==", "path": "volo.abp.authorization.abstractions/8.3.4", "hashPath": "volo.abp.authorization.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.AutoMapper/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-qbVuLzuFIcrTUliiJ5ZPMG6N6g3QSLB7E+cKQ4g4+PHZYE6bqAufdjL8mizP/dkuEGPOi6mEVz5EHe5aCUnhJA==", "path": "volo.abp.automapper/8.3.4", "hashPath": "volo.abp.automapper.8.3.4.nupkg.sha512"}, "Volo.Abp.BackgroundWorkers/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZP8G+ebwqQNBePYYK3goEheCG20SJvXLyS9+bUTTyd2u3ZqnA7twvdL9+oRdVL6rZVWGabZ73T37kiOckyxYNQ==", "path": "volo.abp.backgroundworkers/8.3.4", "hashPath": "volo.abp.backgroundworkers.8.3.4.nupkg.sha512"}, "Volo.Abp.Caching/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-nV0ZC96F7T6sJdw+QikOucXRSI8A2daPdNMMAhoh2hVCU9YrMdSygT4wei8+BWqlhXfAltUotJvrd86FB1N18A==", "path": "volo.abp.caching/8.3.4", "hashPath": "volo.abp.caching.8.3.4.nupkg.sha512"}, "Volo.Abp.Core/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-PhjE9M4df1x0wczDftMH5vgTFOSVixBOPNoRX5dsJFagAIn2Z/xxf4XI6gpvnS77tUNvWoVQZHyiy9cnPCsT2A==", "path": "volo.abp.core/8.3.4", "hashPath": "volo.abp.core.8.3.4.nupkg.sha512"}, "Volo.Abp.Data/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-pKyUySy4CQvOhkzsHt66xDIxvVuARisQV+pA3bAwF3qkGXusV56KoqZ7CUNfD1rMUVf0yvCjsKqRGZsIkIsD7w==", "path": "volo.abp.data/8.3.4", "hashPath": "volo.abp.data.8.3.4.nupkg.sha512"}, "Volo.Abp.Ddd.Application/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-eMsgNDgG1DHKGQ0BDT7QVFuIgOuJ1/cKakEV5b344Fz4o9BE2s9vDBjyHFYkixKprlJ1ubXp58+/aGIY7C6Brg==", "path": "volo.abp.ddd.application/8.3.4", "hashPath": "volo.abp.ddd.application.8.3.4.nupkg.sha512"}, "Volo.Abp.Ddd.Application.Contracts/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-ybs3SqmyJCjdJ9yWzUDur5i0KpnGtn8McYaUFwoTq6VhMm7yFDznGKKw/Xvm2K/pUjfgjgBfdQ2VdLcmr2eGKg==", "path": "volo.abp.ddd.application.contracts/8.3.4", "hashPath": "volo.abp.ddd.application.contracts.8.3.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-NIMuAw7NOLUXA6GSYllO+cMvs/Ggu9t0fsPYR+mBWy/HvDRm1IK3xQFOoWaQVwtdBKaH6nSJ1NTnCSfrSats+A==", "path": "volo.abp.ddd.domain/8.3.4", "hashPath": "volo.abp.ddd.domain.8.3.4.nupkg.sha512"}, "Volo.Abp.Ddd.Domain.Shared/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-GwN4ohVjt2jmol6iBBbBdBIwRHMxKPwaFUE28rbtwVeCgV1Fj1iy8nGhYUxE6qgQtpLsljUvXYhgbr+vrheO0Q==", "path": "volo.abp.ddd.domain.shared/8.3.4", "hashPath": "volo.abp.ddd.domain.shared.8.3.4.nupkg.sha512"}, "Volo.Abp.DistributedLocking.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-RJWB+hVtLDdo6LE+BSdud92G7hVSfoOGcDMEDzqveXrueDtCfeCZHgse1AW5wJT8trU/K+DeRkAFJpie7a7O6g==", "path": "volo.abp.distributedlocking.abstractions/8.3.4", "hashPath": "volo.abp.distributedlocking.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.EventBus/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-j6mnITzG/8X5Ci9caUqOSVH+98I+5CySJ1qwViuARQ3+PhPgsQfFaoZ66SVHmNjbbGkltz9F9U2pQlY4OW+3XA==", "path": "volo.abp.eventbus/8.3.4", "hashPath": "volo.abp.eventbus.8.3.4.nupkg.sha512"}, "Volo.Abp.EventBus.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-kzYlFkxvU6hY+kV0VzBW6BzKnBc/5ZVsg42uNTuUDbxIOpJSEO9/6j0C1WVfmlqEpFhHVc6S9N7/jRBntPmXfw==", "path": "volo.abp.eventbus.abstractions/8.3.4", "hashPath": "volo.abp.eventbus.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.ExceptionHandling/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-c2mwLOE6xfI86VTSjiS0bLW6Yan8F2EVjM3rTw0x9P9d/oEKFZ9kpnac16FxVub/hNxCwYkRESsDj4+oYzJ3Hg==", "path": "volo.abp.exceptionhandling/8.3.4", "hashPath": "volo.abp.exceptionhandling.8.3.4.nupkg.sha512"}, "Volo.Abp.Features/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-n8imibdHcaFYv3gYOc+JKdIQMLwZokFXs22rmvdysyjb4Pno5JTIvYhCi+bpBLE+wtqEomIdzGmuYtao66iWCA==", "path": "volo.abp.features/8.3.4", "hashPath": "volo.abp.features.8.3.4.nupkg.sha512"}, "Volo.Abp.GlobalFeatures/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-SlUj6MB+sCmHat9D1gpy1p1Fy+QY5n+2NlyDwOgvE3PSFaR6+ytFQnSsjPGav6Vve+1CVuPVuDw3lkq2DkoipQ==", "path": "volo.abp.globalfeatures/8.3.4", "hashPath": "volo.abp.globalfeatures.8.3.4.nupkg.sha512"}, "Volo.Abp.Guids/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-H1ZpXblbCotZ43bwhJQ7t6g3DjVh4k/QKJIhBwMHk2Q2qLFfceYfBbUuY/DGhcmVTxcZ4/vKuj1983TDHvrjCA==", "path": "volo.abp.guids/8.3.4", "hashPath": "volo.abp.guids.8.3.4.nupkg.sha512"}, "Volo.Abp.Http.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-QAUwpjwXd7JjlWBOpMIxY1hcqC0bNQzFtgsmVgcd7qSXhnhowpPwXFBHO8BdT1lrqF2he/DK2RtjPh4sZwCeew==", "path": "volo.abp.http.abstractions/8.3.4", "hashPath": "volo.abp.http.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.Json/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-Di3ZykflMInW5GgpTstp/BuOq/Bn4PgVThWlCDYVeCEoCkSkkwWs8BcCuo0BKO2L0TvmaNif5Vg+CvLCO10Upw==", "path": "volo.abp.json/8.3.4", "hashPath": "volo.abp.json.8.3.4.nupkg.sha512"}, "Volo.Abp.Json.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-Wrg0eGBzdeMx0l0FJU02K7UNUJCp0J2Aj2lreH7euo8obWCsYEygabcueRuLXjxUZ8HusrtZvXTc13JsKnQtdA==", "path": "volo.abp.json.abstractions/8.3.4", "hashPath": "volo.abp.json.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.Json.SystemTextJson/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-sGQ7iU2v7kG/YJgWi5oUKHDuyb44JDJdqRBFDzalwnk/duqGp6BPzC3o1vQjVkyAXdQSfW1Sqvmy/nx3DnqxQA==", "path": "volo.abp.json.systemtextjson/8.3.4", "hashPath": "volo.abp.json.systemtextjson.8.3.4.nupkg.sha512"}, "Volo.Abp.Localization/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-lu8p6bGQ70N6+lebW12MAI5EnS3DjXwlYIz4cwX5MY/8iPMzVO4ogJdzMytUOLpaHEQV6iEOGfDHE6xP9hADbg==", "path": "volo.abp.localization/8.3.4", "hashPath": "volo.abp.localization.8.3.4.nupkg.sha512"}, "Volo.Abp.Localization.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-qLejd/5Y+3BJWn3M/0XSX4WI5yg1LIqrcuQ5eVRx2DCxf024Fk9drwmrB0s2YnarEcsTxBq5yoHKgIuvrO4jBA==", "path": "volo.abp.localization.abstractions/8.3.4", "hashPath": "volo.abp.localization.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-BJsWGMqTq4jdZqskb8vZoUT2lyJM/MhKiqbpZcgFOZVj+S/QvjyGh8QELiJjpBKfg1N1PHFTZGzeG4luygXHAg==", "path": "volo.abp.multitenancy/8.3.4", "hashPath": "volo.abp.multitenancy.8.3.4.nupkg.sha512"}, "Volo.Abp.MultiTenancy.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-sJ+oTbXJWpqn91YHmguBeuJHij/OE+R74S5uLew8ktvLafHb5AT0GP4hgQ48dgiYWRuoT1G38fzmN65AeyKB6Q==", "path": "volo.abp.multitenancy.abstractions/8.3.4", "hashPath": "volo.abp.multitenancy.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.ObjectExtending/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-aDh5iy87O+qMuuXc0WMFQYANh1zpyJnACGa48EXfZOfCy3q+DSJdfPZvm0SuIyeSW9ZJSeEkl16r/bfGrqjTlA==", "path": "volo.abp.objectextending/8.3.4", "hashPath": "volo.abp.objectextending.8.3.4.nupkg.sha512"}, "Volo.Abp.ObjectMapping/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-qB8XYJaFxrzEXy5Efegkts8gaOYVvIzXwcuntBxAbxJCW/R7eKRj/+QJWQQA82lVzdgMrW7Z3OFnY1x9NNpTiQ==", "path": "volo.abp.objectmapping/8.3.4", "hashPath": "volo.abp.objectmapping.8.3.4.nupkg.sha512"}, "Volo.Abp.Security/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-A+zocxt9HegZVtP83Dzsw6A2w2gfduVttz3maDrhgfrs92qOVxlncdTn+V6IZ6pabTmdEp6zP0rs6Zdql366Bg==", "path": "volo.abp.security/8.3.4", "hashPath": "volo.abp.security.8.3.4.nupkg.sha512"}, "Volo.Abp.Serialization/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-zGQawUZcEdBjVF2aAhbQ+c9EFuiTxI5S5pxDUDehN6+WLIDfuz+qf3O49iz3c8O1jcSRdxgjMtWfsiq6obc0wg==", "path": "volo.abp.serialization/8.3.4", "hashPath": "volo.abp.serialization.8.3.4.nupkg.sha512"}, "Volo.Abp.Settings/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-pbTQZw/RTVJK+2ufcYPG61FkJn/dybSUVpFAb2Bt6NxWj7D9QDBxgYMNocIimYsOoNRRBpHB/v0HgSEFzcZTkg==", "path": "volo.abp.settings/8.3.4", "hashPath": "volo.abp.settings.8.3.4.nupkg.sha512"}, "Volo.Abp.Specifications/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-iurrAYlDIC+kCI8Mj6vF9sd3qCyyEtljvJBWt4Ysrh50jZzO9jReUisHhw9yzCu6ZNz7g//cbpsXnO1ebJTVvg==", "path": "volo.abp.specifications/8.3.4", "hashPath": "volo.abp.specifications.8.3.4.nupkg.sha512"}, "Volo.Abp.Threading/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-R4bZmjAl8VwvctLFaM92NCLUPok3AnokEMJqI5J8aoDtaPdJ9T6YndJjkMaeKekckEYLp3UcRxt6ytViD6f8eQ==", "path": "volo.abp.threading/8.3.4", "hashPath": "volo.abp.threading.8.3.4.nupkg.sha512"}, "Volo.Abp.Timing/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-SbT6QM2v04J6CjEqy1Ubgh/uZvdVufghFZtgVJkTYQebMZYLCxkHPga5buYGt9Hw8bhhmFmGtidXznJBWbkHOQ==", "path": "volo.abp.timing/8.3.4", "hashPath": "volo.abp.timing.8.3.4.nupkg.sha512"}, "Volo.Abp.Uow/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-KSqhpoyztXEXJ5YrchpbUxydWT045TIBR74yyJo0YFIFLL7VwXNPBesWAIoJcXRG+A/O38DABsiC1Dw6NbQ9QQ==", "path": "volo.abp.uow/8.3.4", "hashPath": "volo.abp.uow.8.3.4.nupkg.sha512"}, "Volo.Abp.Validation/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-kIpcTqM49xpT2ELV3UIqVYwsNLVTY36Q5Gf7+PYyOzYyB/4Gx6vKv2Zx2/J3XTEZTVoGeOoN7Nuxy6NLeW1MpQ==", "path": "volo.abp.validation/8.3.4", "hashPath": "volo.abp.validation.8.3.4.nupkg.sha512"}, "Volo.Abp.Validation.Abstractions/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-/ucmdGyc0Gvt75LrVv0gni1q2fFy4ze0SX8ndmuLkFk7QCERmWI5Ui818ZrOYWanxdj8b8OyFY3UOSL6RDnK9w==", "path": "volo.abp.validation.abstractions/8.3.4", "hashPath": "volo.abp.validation.abstractions.8.3.4.nupkg.sha512"}, "Volo.Abp.VirtualFileSystem/8.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-uPpDF04O0PfwrjH65dG61dFW/JP8vJRTRscRpvJpGGg4/QDStyTIaa2bESVy5DdemqBsI6gzgOYe5YxI9DlYjA==", "path": "volo.abp.virtualfilesystem/8.3.4", "hashPath": "volo.abp.virtualfilesystem.8.3.4.nupkg.sha512"}, "Pe.Application.Contracts/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Pe.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Pe.Domain.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Pe.SqlSugarCore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}