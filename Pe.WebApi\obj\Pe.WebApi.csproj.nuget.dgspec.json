{"format": 1, "restore": {"C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj": {}}, "projects": {"C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\Pe.Application.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\Pe.Application.Contracts.csproj", "projectName": "Pe.Application.Contracts", "projectPath": "C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\Pe.Application.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.Ddd.Application.Contracts": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj", "projectName": "Pe.Application", "projectPath": "C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\Pe.Application.Contracts.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Application.Contracts\\Pe.Application.Contracts.csproj"}, "C:\\Codes\\AbpDemo\\Pe.Domain\\Pe.Domain.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Domain\\Pe.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.Ddd.Application": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj", "projectName": "Pe.Domain.Shared", "projectPath": "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.Ddd.Domain.Shared": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Codes\\AbpDemo\\Pe.Domain\\Pe.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.Domain\\Pe.Domain.csproj", "projectName": "Pe.Domain", "projectPath": "C:\\Codes\\AbpDemo\\Pe.Domain\\Pe.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Domain.Shared\\Pe.Domain.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.Ddd.Domain": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj", "projectName": "Pe.SqlSugarCore", "projectPath": "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj", "projectName": "Pe.WebApi", "projectPath": "C:\\Codes\\AbpDemo\\Pe.WebApi\\Pe.WebApi.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Codes\\AbpDemo\\Pe.WebApi\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.Application\\Pe.Application.csproj"}, "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj": {"projectPath": "C:\\Codes\\AbpDemo\\Pe.SqlSugarCore\\Pe.SqlSugarCore.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Volo.Abp.AspNetCore.Mvc": {"target": "Package", "version": "[8.3.4, )"}, "Volo.Abp.Autofac": {"target": "Package", "version": "[8.3.4, )"}, "Volo.Abp.Swashbuckle": {"target": "Package", "version": "[8.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}